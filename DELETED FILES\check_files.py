import os
import sys
import sqlite3
import pandas as pd

def check_files():
    """Check if the required files exist and create the output file"""
    print(f"Current working directory: {os.getcwd()}")

    # Define file paths
    template_path = os.path.join('data', 'Facility Flex Chart template.xlsx')
    output_path = os.path.join('data', 'Facility Flex Chart LOCAL.xlsx')
    db_path = os.path.join('data', 'Data.db')

    # Check if data directory exists
    if not os.path.exists('data'):
        print("Data directory does not exist. Creating it...")
        os.makedirs('data', exist_ok=True)
    else:
        print("Data directory exists.")

    # List files in data directory
    print("\nFiles in data directory:")
    if os.path.exists('data') and os.path.isdir('data'):
        files = os.listdir('data')
        if files:
            for file in files:
                file_path = os.path.join('data', file)
                file_size = os.path.getsize(file_path) if os.path.isfile(file_path) else "N/A (directory)"
                print(f"  - {file} (Size: {file_size} bytes)")
        else:
            print("  No files found in data directory.")

    # Check template file
    if os.path.exists(template_path):
        print(f"\nTemplate file exists: {template_path}")
        print(f"Size: {os.path.getsize(template_path)} bytes")
    else:
        print(f"\nTemplate file does not exist: {template_path}")

    # Check database file
    if os.path.exists(db_path):
        print(f"\nDatabase file exists: {db_path}")
        print(f"Size: {os.path.getsize(db_path)} bytes")

        # Try to connect to the database and get WorkOrder data
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # Check if WorkOrder table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='WorkOrder'")
            if cursor.fetchone():
                print("\nWorkOrder table exists in the database")

                # Get row count
                cursor.execute("SELECT COUNT(*) FROM WorkOrder")
                row_count = cursor.fetchone()[0]
                print(f"WorkOrder table has {row_count} rows")

                # Get the data
                df = pd.read_sql_query("SELECT * FROM WorkOrder", conn)
                print(f"Retrieved {len(df)} rows from WorkOrder table")

                # Create the output file
                print(f"\nCreating output file: {output_path}")
                df.to_excel(output_path, sheet_name='AppData', index=False)
                print(f"Output file created successfully: {output_path}")
            else:
                print("\nWorkOrder table does not exist in the database")

            conn.close()
        except Exception as e:
            print(f"\nError accessing database: {str(e)}")
    else:
        print(f"\nDatabase file does not exist: {db_path}")

if __name__ == "__main__":
    check_files()
