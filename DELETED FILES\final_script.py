import os
import sqlite3
import pandas as pd
import sys

def create_excel_file():
    """
    Create a new Excel file with the WorkOrder data from the database
    """
    try:
        # Define paths
        data_dir = 'data'
        db_path = os.path.join(data_dir, 'Data.db')
        output_path = os.path.join(data_dir, 'Facility Flex Chart LOCAL.xlsx')
        
        # Create data directory if it doesn't exist
        os.makedirs(data_dir, exist_ok=True)
        
        # Connect to the database
        print(f"Connecting to database: {db_path}")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if WorkOrder table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='WorkOrder'")
        if not cursor.fetchone():
            print("Error: WorkOrder table does not exist in the database")
            conn.close()
            return False
        
        # Get the data from the WorkOrder table
        print("Retrieving data from WorkOrder table...")
        cursor.execute("SELECT * FROM WorkOrder")
        rows = cursor.fetchall()
        
        # Get column names
        cursor.execute("PRAGMA table_info(WorkOrder)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # Create a DataFrame
        df = pd.DataFrame(rows, columns=columns)
        print(f"Retrieved {len(df)} rows from WorkOrder table")
        
        # Close the database connection
        conn.close()
        
        # Create the Excel file
        print(f"Creating Excel file: {output_path}")
        with pd.ExcelWriter(output_path, engine='xlsxwriter') as writer:
            df.to_excel(writer, sheet_name='AppData', index=False)
        
        print(f"Excel file created successfully: {output_path}")
        return True
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

if __name__ == "__main__":
    success = create_excel_file()
    if success:
        print("Script completed successfully")
        sys.exit(0)
    else:
        print("Script failed")
        sys.exit(1)
