from flask import Flask, request, jsonify, render_template, session, redirect, url_for, flash, send_file
# LDAP import removed
from functools import wraps
import sqlite3
import os
import logging
import pandas as pd
from openpyxl import load_workbook
import tempfile
from datetime import datetime
import io

# Initialize the Flask application
app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # Change this to a secure secret key

# Set up logging
logging.basicConfig(level=logging.DEBUG)

# Database setup
DB_PATH = os.path.join('Data', 'Data.db')
os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)

# LDAP Configuration removed

# Add this near the top of the file, where other paths are defined
FLEX_CHART_PATH = os.path.join('data', 'Facility Flex Chart LOCAL.xlsx')

# Make sure the data directory exists
os.makedirs(os.path.dirname(FLEX_CHART_PATH), exist_ok=True)

# Simple authentication function - always return True for any valid user in Authorization table
def authenticate(username, password):
    # Simple authentication - just check if the username exists in the Authorization table
    # Password is ignored in this version since LDAP is down
    # In a real application, you would want to implement proper authentication here
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM Authorization WHERE employee_login = ?", (username,))
        user = cursor.fetchone()
        conn.close()

        if user:
            return True
        return False
    except Exception as e:
        logging.error(f"Authentication Error: {str(e)}")
        flash('Authentication system is unavailable')
        return False

def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'username' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']

        # First check if user exists in Authorization table
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM Authorization WHERE employee_login = ?", (username,))
        authorized_user = cursor.fetchone()
        conn.close()

        if not authorized_user:
            flash('You are not authorized to access this system. Please contact your administrator.')
            return redirect(url_for('login'))

        # If user is authorized, proceed with simple authentication
        if authenticate(username, password):
            session['username'] = username
            # Store the user's access level in the session
            session['access_level'] = authorized_user[3]  # access_level is the 4th column
            session['department'] = authorized_user[1]    # department is the 2nd column
            return redirect(url_for('index'))
        else:
            flash('Invalid credentials')
            return redirect(url_for('login'))

    return render_template('login.html')

@app.route('/logout')
def logout():
    session.pop('username', None)
    return redirect(url_for('login'))

# Protect routes that require authentication
@app.route('/')
@login_required
def index():
    return render_template('sidebar.html')

@app.route('/training')
@login_required
def training():
    return render_template('training.html')

@app.route('/api/upload-workorder', methods=['POST'])
def upload_workorder():
    if 'file' not in request.files:
        return jsonify({'success': False, 'error': 'No file uploaded'})

    file = request.files['file']
    if file.filename == '':
        return jsonify({'success': False, 'error': 'No file selected'})

    if not file.filename.endswith('.xlsx'):
        return jsonify({'success': False, 'error': 'File must be an Excel (.xlsx) file'})

    try:
        # Log the file details
        logging.debug(f"Processing file: {file.filename}")

        # Read the Excel file
        new_df = pd.read_excel(file)
        logging.debug(f"DataFrame columns: {new_df.columns.tolist()}")
        logging.debug(f"New records shape: {new_df.shape}")

        # Connect to the database
        logging.debug(f"Connecting to database at: {os.path.abspath(DB_PATH)}")
        conn = sqlite3.connect(DB_PATH)

        # Drop existing table and recreate without UNIQUE constraint
        conn.execute('DROP TABLE IF EXISTS WorkOrder')
        conn.execute('''CREATE TABLE WorkOrder (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            site TEXT,
            process TEXT,
            factory TEXT,
            owning_department TEXT,
            location TEXT,
            equipment_id TEXT,
            equipment TEXT,
            work_order_number TEXT,
            description TEXT,
            work_order_type TEXT,
            status TEXT,
            assigned_to TEXT,
            operation_description TEXT,
            operation_result TEXT,
            operation_comments TEXT,
            changed_by TEXT,
            status_date DATE,
            activity_number TEXT
        )''')

        # Map new Excel data to database columns
        new_df_mapped = pd.DataFrame({
            'site': new_df['Site'],
            'process': new_df['Process'],
            'factory': new_df['Factory'],
            'owning_department': new_df['Ownng Department'],
            'location': new_df['Location'],
            'equipment_id': new_df['Equipment ID'],
            'equipment': new_df['Equipment'],
            'work_order_number': new_df['WO No.'],
            'description': new_df['WO Description'],
            'work_order_type': new_df['WO Type'],
            'status': new_df['WO Status'],
            'assigned_to': new_df['Labor Name'],
            'operation_description': new_df['OP Description'],
            'operation_result': new_df['OP Result'],
            'operation_comments': new_df['OP Comments'],
            'changed_by': new_df['Changed by Name'],
            'status_date': new_df['Status Date'],
            'activity_number': new_df['Activity No']
        })

        # Get initial count
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM WorkOrder")
        initial_count = cursor.fetchone()[0]

        # Append new data to the table
        new_df_mapped.to_sql('WorkOrder', conn, if_exists='append', index=False)

        # Remove duplicates keeping the latest entry only if ALL columns match
        conn.execute('''
            CREATE TABLE WorkOrder_temp AS
            SELECT * FROM WorkOrder
            WHERE id IN (
                SELECT MAX(id)
                FROM WorkOrder
                GROUP BY site, process, factory, owning_department, location,
                         equipment_id, equipment, work_order_number, description,
                         work_order_type, status, assigned_to, operation_description,
                         operation_result, operation_comments, changed_by,
                         status_date, activity_number
            )
        ''')

        conn.execute('DROP TABLE WorkOrder')
        conn.execute('ALTER TABLE WorkOrder_temp RENAME TO WorkOrder')

        # Get final count
        cursor.execute("SELECT COUNT(*) FROM WorkOrder")
        final_count = cursor.fetchone()[0]

        # Calculate statistics
        total_records = final_count
        new_records = final_count - initial_count
        updated_records = len(new_df_mapped) - new_records

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': f'Upload complete: {new_records} new records added, {updated_records} records updated. Total records: {total_records}'
        })

    except Exception as e:
        logging.error(f"Error during file upload: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route("/employee")

def employee():
    # Get all employees from the database
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row  # This enables column access by name
    cursor = conn.cursor()

    cursor.execute("SELECT * FROM EmployeeList")
    employees = cursor.fetchall()

    # Convert to list of dictionaries
    employee_list = []
    for emp in employees:
        employee_list.append({
            'id': emp['id'],
            'department': emp['department'] if 'department' in emp.keys() else 'Facilities Technician',
            'name': emp['name'],
            'trade': emp['trade'],
            'crew': emp['crew'],
            'start_date': emp['start_date'] if 'start_date' in emp.keys() else '2023-01-01'
        })

    conn.close()

    return render_template("employee.html", employees=employee_list)

@app.route("/api/employees", methods=["GET"])
def get_employees():
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    cursor.execute("SELECT * FROM EmployeeList")
    employees = cursor.fetchall()

    # Convert to list of dictionaries
    employee_list = []
    for emp in employees:
        employee_list.append({
            'id': emp['id'],
            'department': emp['department'] if 'department' in emp.keys() else 'Facilities Technician',
            'name': emp['name'],
            'trade': emp['trade'],
            'crew': emp['crew'],
            'start_date': emp['start_date'] if 'start_date' in emp.keys() else '2023-01-01'
        })

    conn.close()

    return jsonify(employee_list)

@app.route("/api/employees", methods=["POST"])
def add_employee():
    data = request.get_json()

    if not data or 'name' not in data or 'trade' not in data or 'crew' not in data or 'department' not in data or 'start_date' not in data:
        return jsonify({'error': 'Missing required fields'}), 400

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Check if employee already exists
    cursor.execute("SELECT * FROM EmployeeList WHERE name = ? AND trade = ? AND crew = ?",
                  (data['name'], data['trade'], data['crew']))
    existing = cursor.fetchone()

    if existing:
        conn.close()
        return jsonify({'error': 'Employee already exists'}), 409

    # Insert new employee
    cursor.execute("INSERT INTO EmployeeList (department, name, trade, crew, start_date) VALUES (?, ?, ?, ?, ?)",
                  (data['department'], data['name'], data['trade'], data['crew'], data['start_date']))

    conn.commit()
    new_id = cursor.lastrowid
    conn.close()

    return jsonify({
        'id': new_id,
        'department': data['department'],
        'name': data['name'],
        'trade': data['trade'],
        'crew': data['crew'],
        'start_date': data['start_date']
    }), 201

@app.route("/api/employees/<int:employee_id>", methods=["DELETE"])
def delete_employee(employee_id):
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    cursor.execute("DELETE FROM EmployeeList WHERE id = ?", (employee_id,))

    if cursor.rowcount == 0:
        conn.close()
        return jsonify({'error': 'Employee not found'}), 404

    conn.commit()
    conn.close()

    return jsonify({'message': 'Employee deleted successfully'})

@app.route('/api/check-workorders', methods=['GET'])
def check_workorders():
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute("""
            SELECT work_order_number, assigned_to, description,
                   status, status_date, activity_number, owning_department
            FROM WorkOrder
            ORDER BY owning_department, activity_number, assigned_to, status_date
        """)
        records = [dict(row) for row in cursor.fetchall()]

        conn.close()

        return jsonify({
            'status': 'success',
            'records': records
        })

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        })

@app.route('/analytics')
def analytics():
    return render_template('ganttchart.html')

@app.route('/authorization')
@login_required
def authorization():
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    cursor.execute('''CREATE TABLE IF NOT EXISTS Authorization (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        department TEXT NOT NULL,
        employee_login TEXT NOT NULL,
        access_level TEXT NOT NULL,
        UNIQUE(department, employee_login)
    )''')
    conn.commit()

    cursor.execute("SELECT * FROM Authorization")
    authorizations = [dict(row) for row in cursor.fetchall()]
    conn.close()

    return render_template('authorization.html', authorizations=authorizations)

@app.route('/api/authorizations', methods=['POST'])
def add_authorization():
    data = request.get_json()

    if not data or 'department' not in data or 'employee_login' not in data or 'access_level' not in data:
        return jsonify({'error': 'Missing required fields'}), 400

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        cursor.execute(
            "INSERT INTO Authorization (department, employee_login, access_level) VALUES (?, ?, ?)",
            (data['department'], data['employee_login'], data['access_level'])
        )
        conn.commit()

        # Return the new authorization data including the ID
        new_id = cursor.lastrowid
        return jsonify({
            'id': new_id,
            'department': data['department'],
            'employee_login': data['employee_login'],
            'access_level': data['access_level']
        }), 201
    except sqlite3.IntegrityError:
        return jsonify({'error': 'Authorization already exists'}), 409
    finally:
        conn.close()

@app.route('/api/authorizations/<int:auth_id>', methods=['DELETE'])
def delete_authorization(auth_id):
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    cursor.execute("DELETE FROM Authorization WHERE id = ?", (auth_id,))

    if cursor.rowcount == 0:
        conn.close()
        return jsonify({'error': 'Authorization not found'}), 404

    conn.commit()
    conn.close()

    return jsonify({'message': 'Authorization deleted successfully'})

@app.route('/calendar')
@login_required
def calendar():
    return render_template('calendar.html')

@app.route('/api/events', methods=['GET'])
def get_events():
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute("""
            SELECT
                work_order_number as id,
                description as title,
                status_date as start,
                DATETIME(status_date, '+1 hour') as end,  -- Set end time to 1 hour after start
                work_order_type as type,
                status,
                assigned_to
            FROM WorkOrder
            WHERE status_date IS NOT NULL
        """)
        events = [dict(row) for row in cursor.fetchall()]
        conn.close()

        return jsonify(events)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/events', methods=['POST'])
@login_required
def create_event():
    data = request.json
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        cursor.execute("""
            INSERT INTO Events (title, start_time, end_time, event_type)
            VALUES (?, ?, ?, ?)
        """, (data['title'], data['start'], data['end'], data['type']))
        conn.commit()
        event_id = cursor.lastrowid
        conn.close()
        return jsonify({'success': True, 'id': event_id})
    except Exception as e:
        conn.close()
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/events/<int:event_id>', methods=['DELETE', 'PUT'])
@login_required
def manage_event(event_id):
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        if request.method == 'DELETE':
            cursor.execute("DELETE FROM Events WHERE id = ?", (event_id,))
        else:  # PUT
            data = request.json
            cursor.execute("""
                UPDATE Events
                SET title = ?, start_time = ?, end_time = ?, event_type = ?
                WHERE id = ?
            """, (data['title'], data['start'], data['end'], data['type'], event_id))

        conn.commit()
        conn.close()
        return jsonify({'success': True})
    except Exception as e:
        conn.close()
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/download-flex-chart', methods=['GET'])
def download_flex_chart():
    try:
        # Define the path to the template file
        template_path = os.path.join('data', 'Facility Flex Chart template.xlsx')

        # Read the file into memory to avoid modifying the original
        with open(template_path, 'rb') as f:
            file_data = io.BytesIO(f.read())

        # Return it as a downloadable file
        return send_file(
            file_data,
            as_attachment=True,
            download_name='Facility Flex Chart LOCAL.xlsx',
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        print(f"Error during download: {e}")

@app.route('/organization-chart')
@login_required
def organization_chart():
    return render_template('organization_chart.html')

if __name__ == "__main__":
    app.run(host="127.0.0.1", port=3001, debug=True)
