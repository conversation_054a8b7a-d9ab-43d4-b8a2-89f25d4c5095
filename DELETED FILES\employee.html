<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Employee Management</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='sidebar.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@24,400,0,0">
</head>
<body>
    {% include 'sidebar.html' %}
    <div class="container">
        <h1>Employee Management</h1>

        <div class="table-container">
            <h2>Add New Employee</h2>
            <table id="addEmployeeTable">
                <thead>
                    <tr>
                        <th>Department</th>
                        <th>Employee Name</th>
                        <th>Trade</th>
                        <th>Crew</th>
                        <th>Start Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <select id="department" required>
                                <option value="">Select a department...</option>
                                <option value="Facilities Technician">Facilities Technician</option>
                                <option value="Facilities Operator">Facilities Operator</option>
                            </select>
                        </td>
                        <td>
                            <input type="text" id="employeeName" required>
                        </td>
                        <td>
                            <select id="trade" required>
                                <option value="">Select a trade...</option>
                                <option value="Boiler">Boiler</option>
                                <option value="Crew Leader">Crew Leader</option>
                                <option value="Electrical">Electrical</option>
                                <option value="HVAC">HVAC</option>
                                <option value="Mechanical">Mechanical</option>
                                <option value="Operator">Operator</option>
                                <option value="Driver">Driver</option>
                                <option value="Supervisor">Supervisor</option>
                            </select>
                        </td>
                        <td>
                            <select id="crew" required>
                                <option value="">Select a crew...</option>
                                <option value="A">A</option>
                                <option value="B">B</option>
                                <option value="C">C</option>
                                <option value="D">D</option>
                            </select>
                        </td>
                        <td>
                            <input type="date" id="startDate" required>
                        </td>
                        <td>
                            <button type="button" id="addEmployeeBtn" class="btn-add">Add</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="table-container">
            <h2>Employee List</h2>
            <table id="employeeTable">
                <thead>
                    <tr>
                        <th>Department</th>
                        <th>Employee Name</th>
                        <th>Trade</th>
                        <th>Crew</th>
                        <th>Start Date</th>
                        <th>Actions</th>
                    </tr>
                    <tr class="filter-row">
                        <th><input type="text" class="table-filter" id="departmentFilter" placeholder="Filter by department..."></th>
                        <th><input type="text" class="table-filter" id="nameFilter" placeholder="Filter by name..."></th>
                        <th><input type="text" class="table-filter" id="tradeFilter" placeholder="Filter by trade..."></th>
                        <th><input type="text" class="table-filter" id="crewFilter" placeholder="Filter by crew..."></th>
                        <th><input type="text" class="table-filter" id="dateFilter" placeholder="Filter by date..."></th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Table rows will be loaded from the database and can be modified dynamically -->
                    {% if employees %}
                        {% for employee in employees %}
                        <tr data-id="{{ employee.id }}">
                            <td>{{ employee.department }}</td>
                            <td>{{ employee.name }}</td>
                            <td>{{ employee.trade }}</td>
                            <td>{{ employee.crew }}</td>
                            <td>{{ employee.start_date }}</td>
                            <td>
                                <button class="btn-delete" onclick="deleteEmployee('{{ employee.id }}')">Delete</button>
                            </td>
                        </tr>
                        {% endfor %}
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>

    <script src="{{ url_for('static', filename='sidebar.js') }}"></script>
    <script src="{{ url_for('static', filename='employee.js') }}"></script>
</body>
</html>



