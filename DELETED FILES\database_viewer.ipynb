{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Database Viewer\n", "\n", "This notebook connects to the SQLite database and displays the contents of the EmployeeList table."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Database path: c:\\Users\\<USER>\\Desktop\\MyWebsite\\UltiumApp\\Data\\Data.db\n", "Database file exists: Data\\Data.db\n"]}], "source": ["import sqlite3\n", "import os\n", "\n", "# Database path - make sure this matches the path in project.py\n", "DB_PATH = os.path.join('Data', 'Data.db')\n", "print(f\"Database path: {os.path.abspath(DB_PATH)}\")\n", "\n", "# Check if the database file exists\n", "if os.path.exists(DB_PATH):\n", "    print(f\"Database file exists: {DB_PATH}\")\n", "else:\n", "    print(f\"Database file does not exist: {DB_PATH}\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tables in the database:\n", "  employeelist\n", "  sqlite_sequence\n", "  WorkOrder_temp\n", "  Authorization\n", "  AppData\n", "  <PERSON><PERSON><PERSON><PERSON>\n", "  WorkOrder\n"]}], "source": ["def print_database_schema():\n", "    # Connect to the SQLite database\n", "    conn = sqlite3.connect(DB_PATH)\n", "    cursor = conn.cursor()\n", "    \n", "    # Get list of tables\n", "    cursor.execute(\"SELECT name FROM sqlite_master WHERE type='table';\")\n", "    tables = cursor.fetchall()\n", "    \n", "    print(\"Tables in the database:\")\n", "    for table in tables:\n", "        print(f\"  {table[0]}\")\n", "    \n", "    # Close the connection\n", "    conn.close()\n", "\n", "# Print the database schema\n", "print_database_schema()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "WorkOrder table columns:\n", "  id (INTEGER)\n", "  site (TEXT)\n", "  process (TEXT)\n", "  factory (TEXT)\n", "  owning_department (TEXT)\n", "  location (TEXT)\n", "  equipment_id (TEXT)\n", "  equipment (TEXT)\n", "  work_order_number (TEXT)\n", "  stage (TEXT)\n", "  description (TEXT)\n", "  work_order_type (TEXT)\n", "  status (TEXT)\n", "  assigned_to (TEXT)\n", "  operation_description (TEXT)\n", "  operation_result (TEXT)\n", "  operation_comments (TEXT)\n", "  changed_by (TEXT)\n", "  status_date (DATE)\n", "  activity_number (TEXT)\n"]}], "source": ["def print_workorder_schema():\n", "    # Connect to the SQLite database\n", "    conn = sqlite3.connect(DB_PATH)\n", "    cursor = conn.cursor()\n", "    \n", "    try:\n", "        # Print table schema\n", "        print(\"\\nWorkOrder table columns:\")\n", "        cursor.execute(\"PRAGMA table_info(WorkOrder)\")\n", "        schema = cursor.fetchall()\n", "        \n", "        # Print column names and their types\n", "        for column in schema:\n", "            column_name = column[1]  # column name is in index 1\n", "            column_type = column[2]  # column type is in index 2\n", "            print(f\"  {column_name} ({column_type})\")\n", "            \n", "    except sqlite3.OperationalError as e:\n", "        print(f\"Error: {e}\")\n", "        \n", "    # Close the connection\n", "    conn.close()\n", "\n", "# Print the WorkOrder table columns\n", "print_workorder_schema()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-05-05 13:12:55,484 - INFO - Starting WorkOrder data update process...\n", "2025-05-05 13:12:55,485 - INFO - Reading data from: c:\\Users\\<USER>\\Desktop\\MyWebsite\\UltiumApp\\employee_data_excel.xlsx\n", "2025-05-05 13:13:01,074 - INFO - Read 62175 records from Excel file\n", "2025-05-05 13:13:01,075 - INFO - Connecting to database: c:\\Users\\<USER>\\Desktop\\MyWebsite\\UltiumApp\\data\\Data.db\n", "2025-05-05 13:13:01,076 - INFO - Dropping existing WorkOrder table\n", "2025-05-05 13:13:01,095 - INFO - Creating new WorkOrder table\n", "2025-05-05 13:13:01,098 - INFO - Inserting data into WorkOrder table\n", "2025-05-05 13:13:01,373 - INFO - Successfully updated WorkOrder table with 62175 records\n"]}, {"name": "stdout", "output_type": "stream", "text": ["WorkOrder data update completed successfully.\n"]}], "source": ["import pandas as pd\n", "import sqlite3\n", "import os\n", "import logging\n", "\n", "# Set up logging\n", "logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')\n", "\n", "def update_workorder_data():\n", "    try:\n", "        # Define file paths\n", "        excel_file = os.path.abspath('employee_data_excel.xlsx')\n", "        db_path = os.path.abspath(os.path.join('data', 'Data.db'))\n", "        \n", "        # Check if files exist\n", "        if not os.path.exists(excel_file):\n", "            logging.error(f\"Error: Excel file '{excel_file}' not found.\")\n", "            return False\n", "        \n", "        if not os.path.exists(db_path):\n", "            logging.error(f\"Error: Database file '{db_path}' not found.\")\n", "            return False\n", "        \n", "        logging.info(\"Starting WorkOrder data update process...\")\n", "        \n", "        # Read the Excel file\n", "        logging.info(f\"Reading data from: {excel_file}\")\n", "        df = pd.read_excel(excel_file)\n", "        logging.info(f\"Read {len(df)} records from Excel file\")\n", "        \n", "        # Verify and map columns to match the WorkOrder table schema\n", "        required_columns = [\n", "            'site', 'process', 'factory', 'owning_department', 'location', \n", "            'equipment_id', 'equipment', 'work_order_number', 'stage', 'description', \n", "            'work_order_type', 'status', 'assigned_to', 'operation_description', \n", "            'operation_result', 'operation_comments', 'changed_by', 'status_date', \n", "            'activity_number'\n", "        ]\n", "        \n", "        # Create a connection to the SQLite database\n", "        logging.info(f\"Connecting to database: {db_path}\")\n", "        conn = sqlite3.connect(db_path)\n", "        cursor = conn.cursor()\n", "        \n", "        # Drop existing WorkOrder table and recreate it\n", "        logging.info(\"Dropping existing WorkOrder table\")\n", "        cursor.execute('DROP TABLE IF EXISTS WorkOrder')\n", "        \n", "        logging.info(\"Creating new WorkOrder table\")\n", "        cursor.execute('''CREATE TABLE WorkOrder (\n", "            id INTEGER PRIMARY KEY AUTOINCREMENT,\n", "            site TEXT,\n", "            process TEXT,\n", "            factory TEXT,\n", "            owning_department TEXT,\n", "            location TEXT,\n", "            equipment_id TEXT,\n", "            equipment TEXT,\n", "            work_order_number TEXT,\n", "            stage TEXT,\n", "            description TEXT,\n", "            work_order_type TEXT,\n", "            status TEXT,\n", "            assigned_to TEXT,\n", "            operation_description TEXT,\n", "            operation_result TEXT,\n", "            operation_comments TEXT,\n", "            changed_by TEXT,\n", "            status_date DATE,\n", "            activity_number TEXT\n", "        )''')\n", "        \n", "        # Insert data from Excel file into the database\n", "        logging.info(\"Inserting data into WorkOrder table\")\n", "        df.to_sql('WorkOrder', conn, if_exists='append', index=False)\n", "        \n", "        # Commit changes and close connection\n", "        conn.commit()\n", "        conn.close()\n", "        \n", "        logging.info(f\"Successfully updated WorkOrder table with {len(df)} records\")\n", "        return True\n", "        \n", "    except Exception as e:\n", "        logging.error(f\"Error during WorkOrder data update: {str(e)}\", exc_info=True)\n", "        return False\n", "\n", "if __name__ == \"__main__\":\n", "    success = update_workorder_data()\n", "    if success:\n", "        print(\"WorkOrder data update completed successfully.\")\n", "    else:\n", "        print(\"WorkOrder data update failed. Check logs for details.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "WorkOrder table columns:\n", "  id (INTEGER)\n", "  site (TEXT)\n", "  process (TEXT)\n", "  factory (TEXT)\n", "  owning_department (TEXT)\n", "  location (TEXT)\n", "  equipment_id (TEXT)\n", "  equipment (TEXT)\n", "  work_order_number (TEXT)\n", "  stage (TEXT)\n", "  description (TEXT)\n", "  work_order_type (TEXT)\n", "  status (TEXT)\n", "  assigned_to (TEXT)\n", "  operation_description (TEXT)\n", "  operation_result (TEXT)\n", "  operation_comments (TEXT)\n", "  changed_by (TEXT)\n", "  status_date (DATE)\n", "  activity_number (TEXT)\n"]}], "source": ["print_workorder_schema()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "First 5 rows of WorkOrder table:\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-30 11:02:22,586 - DEBUG - matplotlib data path: c:\\Users\\<USER>\\.vscode\\Lib\\site-packages\\matplotlib\\mpl-data\n", "2025-04-30 11:02:22,614 - DEBUG - CONFIGDIR=C:\\Users\\<USER>\\.matplotlib\n", "2025-04-30 11:02:22,777 - DEBUG - interactive is False\n", "2025-04-30 11:02:22,778 - DEBUG - platform is win32\n", "2025-04-30 11:02:23,086 - DEBUG - CACHEDIR=C:\\Users\\<USER>\\.matplotlib\n", "2025-04-30 11:02:23,101 - DEBUG - Using fontManager instance from C:\\Users\\<USER>\\.matplotlib\\fontlist-v390.json\n"]}, {"data": {"text/html": ["<style type=\"text/css\">\n", "#T_e9921 th {\n", "  text-align: left;\n", "  font-size: 10pt;\n", "}\n", "#T_e9921_row0_col0, #T_e9921_row0_col1, #T_e9921_row0_col2, #T_e9921_row0_col3, #T_e9921_row0_col4, #T_e9921_row0_col5, #T_e9921_row0_col6, #T_e9921_row0_col7, #T_e9921_row0_col8, #T_e9921_row0_col9, #T_e9921_row0_col10, #T_e9921_row0_col11, #T_e9921_row0_col12, #T_e9921_row0_col13, #T_e9921_row0_col14, #T_e9921_row0_col15, #T_e9921_row0_col16, #T_e9921_row0_col17, #T_e9921_row0_col18, #T_e9921_row0_col19, #T_e9921_row1_col0, #T_e9921_row1_col1, #T_e9921_row1_col2, #T_e9921_row1_col3, #T_e9921_row1_col4, #T_e9921_row1_col5, #T_e9921_row1_col6, #T_e9921_row1_col7, #T_e9921_row1_col8, #T_e9921_row1_col9, #T_e9921_row1_col10, #T_e9921_row1_col11, #T_e9921_row1_col12, #T_e9921_row1_col13, #T_e9921_row1_col14, #T_e9921_row1_col15, #T_e9921_row1_col16, #T_e9921_row1_col17, #T_e9921_row1_col18, #T_e9921_row1_col19, #T_e9921_row2_col0, #T_e9921_row2_col1, #T_e9921_row2_col2, #T_e9921_row2_col3, #T_e9921_row2_col4, #T_e9921_row2_col5, #T_e9921_row2_col6, #T_e9921_row2_col7, #T_e9921_row2_col8, #T_e9921_row2_col9, #T_e9921_row2_col10, #T_e9921_row2_col11, #T_e9921_row2_col12, #T_e9921_row2_col13, #T_e9921_row2_col14, #T_e9921_row2_col15, #T_e9921_row2_col16, #T_e9921_row2_col17, #T_e9921_row2_col18, #T_e9921_row2_col19, #T_e9921_row3_col0, #T_e9921_row3_col1, #T_e9921_row3_col2, #T_e9921_row3_col3, #T_e9921_row3_col4, #T_e9921_row3_col5, #T_e9921_row3_col6, #T_e9921_row3_col7, #T_e9921_row3_col8, #T_e9921_row3_col9, #T_e9921_row3_col10, #T_e9921_row3_col11, #T_e9921_row3_col12, #T_e9921_row3_col13, #T_e9921_row3_col14, #T_e9921_row3_col15, #T_e9921_row3_col16, #T_e9921_row3_col17, #T_e9921_row3_col18, #T_e9921_row3_col19, #T_e9921_row4_col0, #T_e9921_row4_col1, #T_e9921_row4_col2, #T_e9921_row4_col3, #T_e9921_row4_col4, #T_e9921_row4_col5, #T_e9921_row4_col6, #T_e9921_row4_col7, #T_e9921_row4_col8, #T_e9921_row4_col9, #T_e9921_row4_col10, #T_e9921_row4_col11, #T_e9921_row4_col12, #T_e9921_row4_col13, #T_e9921_row4_col14, #T_e9921_row4_col15, #T_e9921_row4_col16, #T_e9921_row4_col17, #T_e9921_row4_col18, #T_e9921_row4_col19 {\n", "  text-align: left;\n", "  white-space: pre-wrap;\n", "  font-size: 10pt;\n", "}\n", "</style>\n", "<table id=\"T_e9921\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_e9921_level0_col0\" class=\"col_heading level0 col0\" >id</th>\n", "      <th id=\"T_e9921_level0_col1\" class=\"col_heading level0 col1\" >site</th>\n", "      <th id=\"T_e9921_level0_col2\" class=\"col_heading level0 col2\" >process</th>\n", "      <th id=\"T_e9921_level0_col3\" class=\"col_heading level0 col3\" >factory</th>\n", "      <th id=\"T_e9921_level0_col4\" class=\"col_heading level0 col4\" >owning_department</th>\n", "      <th id=\"T_e9921_level0_col5\" class=\"col_heading level0 col5\" >location</th>\n", "      <th id=\"T_e9921_level0_col6\" class=\"col_heading level0 col6\" >equipment_id</th>\n", "      <th id=\"T_e9921_level0_col7\" class=\"col_heading level0 col7\" >equipment</th>\n", "      <th id=\"T_e9921_level0_col8\" class=\"col_heading level0 col8\" >work_order_number</th>\n", "      <th id=\"T_e9921_level0_col9\" class=\"col_heading level0 col9\" >stage</th>\n", "      <th id=\"T_e9921_level0_col10\" class=\"col_heading level0 col10\" >description</th>\n", "      <th id=\"T_e9921_level0_col11\" class=\"col_heading level0 col11\" >work_order_type</th>\n", "      <th id=\"T_e9921_level0_col12\" class=\"col_heading level0 col12\" >status</th>\n", "      <th id=\"T_e9921_level0_col13\" class=\"col_heading level0 col13\" >assigned_to</th>\n", "      <th id=\"T_e9921_level0_col14\" class=\"col_heading level0 col14\" >operation_description</th>\n", "      <th id=\"T_e9921_level0_col15\" class=\"col_heading level0 col15\" >operation_result</th>\n", "      <th id=\"T_e9921_level0_col16\" class=\"col_heading level0 col16\" >operation_comments</th>\n", "      <th id=\"T_e9921_level0_col17\" class=\"col_heading level0 col17\" >changed_by</th>\n", "      <th id=\"T_e9921_level0_col18\" class=\"col_heading level0 col18\" >status_date</th>\n", "      <th id=\"T_e9921_level0_col19\" class=\"col_heading level0 col19\" >activity_number</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_e9921_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_e9921_row0_col0\" class=\"data row0 col0\" >1</td>\n", "      <td id=\"T_e9921_row0_col1\" class=\"data row0 col1\" >UTIL.GM</td>\n", "      <td id=\"T_e9921_row0_col2\" class=\"data row0 col2\" >Utility</td>\n", "      <td id=\"T_e9921_row0_col3\" class=\"data row0 col3\" >GM1 COMMON</td>\n", "      <td id=\"T_e9921_row0_col4\" class=\"data row0 col4\" >Electrical</td>\n", "      <td id=\"T_e9921_row0_col5\" class=\"data row0 col5\" >100</td>\n", "      <td id=\"T_e9921_row0_col6\" class=\"data row0 col6\" >U1UBCU00101-002-001</td>\n", "      <td id=\"T_e9921_row0_col7\" class=\"data row0 col7\" >BCU-100 ?-1002 ?-FILTER</td>\n", "      <td id=\"T_e9921_row0_col8\" class=\"data row0 col8\" >GM7057977</td>\n", "      <td id=\"T_e9921_row0_col9\" class=\"data row0 col9\" >Electrical Stage 1</td>\n", "      <td id=\"T_e9921_row0_col10\" class=\"data row0 col10\" >Assembly and Installation Of Parts</td>\n", "      <td id=\"T_e9921_row0_col11\" class=\"data row0 col11\" >PP</td>\n", "      <td id=\"T_e9921_row0_col12\" class=\"data row0 col12\" >DRAFT</td>\n", "      <td id=\"T_e9921_row0_col13\" class=\"data row0 col13\" >Koby Wear</td>\n", "      <td id=\"T_e9921_row0_col14\" class=\"data row0 col14\" >Electrical Stage 1: Observing assembly and installation of parts</td>\n", "      <td id=\"T_e9921_row0_col15\" class=\"data row0 col15\" >10 : OK</td>\n", "      <td id=\"T_e9921_row0_col16\" class=\"data row0 col16\" >TEST</td>\n", "      <td id=\"T_e9921_row0_col17\" class=\"data row0 col17\" ><PERSON></td>\n", "      <td id=\"T_e9921_row0_col18\" class=\"data row0 col18\" >2023-01-01 00:00:00</td>\n", "      <td id=\"T_e9921_row0_col19\" class=\"data row0 col19\" >A00208147</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9921_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_e9921_row1_col0\" class=\"data row1 col0\" >2</td>\n", "      <td id=\"T_e9921_row1_col1\" class=\"data row1 col1\" >UTIL.GM</td>\n", "      <td id=\"T_e9921_row1_col2\" class=\"data row1 col2\" >Utility</td>\n", "      <td id=\"T_e9921_row1_col3\" class=\"data row1 col3\" >GM1 COMMON</td>\n", "      <td id=\"T_e9921_row1_col4\" class=\"data row1 col4\" >Electrical</td>\n", "      <td id=\"T_e9921_row1_col5\" class=\"data row1 col5\" >100</td>\n", "      <td id=\"T_e9921_row1_col6\" class=\"data row1 col6\" >U1UBCU00101-002-001</td>\n", "      <td id=\"T_e9921_row1_col7\" class=\"data row1 col7\" >BCU-100 ?-1002 ?-FILTER</td>\n", "      <td id=\"T_e9921_row1_col8\" class=\"data row1 col8\" >GM7057977</td>\n", "      <td id=\"T_e9921_row1_col9\" class=\"data row1 col9\" >Electrical Stage 1</td>\n", "      <td id=\"T_e9921_row1_col10\" class=\"data row1 col10\" >Assembly and Installation Of Parts</td>\n", "      <td id=\"T_e9921_row1_col11\" class=\"data row1 col11\" >PP</td>\n", "      <td id=\"T_e9921_row1_col12\" class=\"data row1 col12\" >INPRG</td>\n", "      <td id=\"T_e9921_row1_col13\" class=\"data row1 col13\" >Koby Wear</td>\n", "      <td id=\"T_e9921_row1_col14\" class=\"data row1 col14\" >Electrical Stage 1: Observing assembly and installation of parts</td>\n", "      <td id=\"T_e9921_row1_col15\" class=\"data row1 col15\" >11 : OK</td>\n", "      <td id=\"T_e9921_row1_col16\" class=\"data row1 col16\" >TEST</td>\n", "      <td id=\"T_e9921_row1_col17\" class=\"data row1 col17\" ><PERSON></td>\n", "      <td id=\"T_e9921_row1_col18\" class=\"data row1 col18\" >2023-01-02 23:55:54.992000</td>\n", "      <td id=\"T_e9921_row1_col19\" class=\"data row1 col19\" >A00208147</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9921_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_e9921_row2_col0\" class=\"data row2 col0\" >3</td>\n", "      <td id=\"T_e9921_row2_col1\" class=\"data row2 col1\" >UTIL.GM</td>\n", "      <td id=\"T_e9921_row2_col2\" class=\"data row2 col2\" >Utility</td>\n", "      <td id=\"T_e9921_row2_col3\" class=\"data row2 col3\" >GM1 COMMON</td>\n", "      <td id=\"T_e9921_row2_col4\" class=\"data row2 col4\" >Electrical</td>\n", "      <td id=\"T_e9921_row2_col5\" class=\"data row2 col5\" >100</td>\n", "      <td id=\"T_e9921_row2_col6\" class=\"data row2 col6\" >U1UBCU00101-002-001</td>\n", "      <td id=\"T_e9921_row2_col7\" class=\"data row2 col7\" >BCU-100 ?-1002 ?-FILTER</td>\n", "      <td id=\"T_e9921_row2_col8\" class=\"data row2 col8\" >GM7057977</td>\n", "      <td id=\"T_e9921_row2_col9\" class=\"data row2 col9\" >Electrical Stage 1</td>\n", "      <td id=\"T_e9921_row2_col10\" class=\"data row2 col10\" >Assembly and Installation Of Parts</td>\n", "      <td id=\"T_e9921_row2_col11\" class=\"data row2 col11\" >PP</td>\n", "      <td id=\"T_e9921_row2_col12\" class=\"data row2 col12\" >OPCOMP</td>\n", "      <td id=\"T_e9921_row2_col13\" class=\"data row2 col13\" >Koby Wear</td>\n", "      <td id=\"T_e9921_row2_col14\" class=\"data row2 col14\" >Electrical Stage 1: Observing assembly and installation of parts</td>\n", "      <td id=\"T_e9921_row2_col15\" class=\"data row2 col15\" >12 : OK</td>\n", "      <td id=\"T_e9921_row2_col16\" class=\"data row2 col16\" >TEST</td>\n", "      <td id=\"T_e9921_row2_col17\" class=\"data row2 col17\" ><PERSON></td>\n", "      <td id=\"T_e9921_row2_col18\" class=\"data row2 col18\" >2023-01-04 02:35:28.589000</td>\n", "      <td id=\"T_e9921_row2_col19\" class=\"data row2 col19\" >A00208147</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9921_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_e9921_row3_col0\" class=\"data row3 col0\" >4</td>\n", "      <td id=\"T_e9921_row3_col1\" class=\"data row3 col1\" >UTIL.GM</td>\n", "      <td id=\"T_e9921_row3_col2\" class=\"data row3 col2\" >Utility</td>\n", "      <td id=\"T_e9921_row3_col3\" class=\"data row3 col3\" >GM1 COMMON</td>\n", "      <td id=\"T_e9921_row3_col4\" class=\"data row3 col4\" >Electrical</td>\n", "      <td id=\"T_e9921_row3_col5\" class=\"data row3 col5\" >100</td>\n", "      <td id=\"T_e9921_row3_col6\" class=\"data row3 col6\" >U1UBCU00101-002-001</td>\n", "      <td id=\"T_e9921_row3_col7\" class=\"data row3 col7\" >BCU-100 ?-1002 ?-FILTER</td>\n", "      <td id=\"T_e9921_row3_col8\" class=\"data row3 col8\" >GM7057977</td>\n", "      <td id=\"T_e9921_row3_col9\" class=\"data row3 col9\" >Electrical Stage 1</td>\n", "      <td id=\"T_e9921_row3_col10\" class=\"data row3 col10\" >Assembly and Installation Of Parts</td>\n", "      <td id=\"T_e9921_row3_col11\" class=\"data row3 col11\" >PP</td>\n", "      <td id=\"T_e9921_row3_col12\" class=\"data row3 col12\" >COMP</td>\n", "      <td id=\"T_e9921_row3_col13\" class=\"data row3 col13\" >Koby Wear</td>\n", "      <td id=\"T_e9921_row3_col14\" class=\"data row3 col14\" >Electrical Stage 1: Observing assembly and installation of parts</td>\n", "      <td id=\"T_e9921_row3_col15\" class=\"data row3 col15\" >13 : OK</td>\n", "      <td id=\"T_e9921_row3_col16\" class=\"data row3 col16\" >TEST</td>\n", "      <td id=\"T_e9921_row3_col17\" class=\"data row3 col17\" ><PERSON></td>\n", "      <td id=\"T_e9921_row3_col18\" class=\"data row3 col18\" >2023-01-06 14:16:31.017000</td>\n", "      <td id=\"T_e9921_row3_col19\" class=\"data row3 col19\" >A00208147</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e9921_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_e9921_row4_col0\" class=\"data row4 col0\" >5</td>\n", "      <td id=\"T_e9921_row4_col1\" class=\"data row4 col1\" >UTIL.GM</td>\n", "      <td id=\"T_e9921_row4_col2\" class=\"data row4 col2\" >Utility</td>\n", "      <td id=\"T_e9921_row4_col3\" class=\"data row4 col3\" >GM1 COMMON</td>\n", "      <td id=\"T_e9921_row4_col4\" class=\"data row4 col4\" >Electrical</td>\n", "      <td id=\"T_e9921_row4_col5\" class=\"data row4 col5\" >100</td>\n", "      <td id=\"T_e9921_row4_col6\" class=\"data row4 col6\" >U1UBCU00101-002-001</td>\n", "      <td id=\"T_e9921_row4_col7\" class=\"data row4 col7\" >BCU-100 ?-1002 ?-FILTER</td>\n", "      <td id=\"T_e9921_row4_col8\" class=\"data row4 col8\" >GM7057977</td>\n", "      <td id=\"T_e9921_row4_col9\" class=\"data row4 col9\" >Electrical Stage 1</td>\n", "      <td id=\"T_e9921_row4_col10\" class=\"data row4 col10\" >Assembly and Installation Of Parts</td>\n", "      <td id=\"T_e9921_row4_col11\" class=\"data row4 col11\" >PP</td>\n", "      <td id=\"T_e9921_row4_col12\" class=\"data row4 col12\" >CLOSE</td>\n", "      <td id=\"T_e9921_row4_col13\" class=\"data row4 col13\" >Koby Wear</td>\n", "      <td id=\"T_e9921_row4_col14\" class=\"data row4 col14\" >Electrical Stage 1: Observing assembly and installation of parts</td>\n", "      <td id=\"T_e9921_row4_col15\" class=\"data row4 col15\" >14 : OK</td>\n", "      <td id=\"T_e9921_row4_col16\" class=\"data row4 col16\" >TEST</td>\n", "      <td id=\"T_e9921_row4_col17\" class=\"data row4 col17\" ><PERSON></td>\n", "      <td id=\"T_e9921_row4_col18\" class=\"data row4 col18\" >2023-01-09 04:50:21.371000</td>\n", "      <td id=\"T_e9921_row4_col19\" class=\"data row4 col19\" >A00208147</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x1f63ab20080>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def print_workorder_head(n=5):\n", "    \"\"\"\n", "    Print the first n rows of the WorkOrder table\n", "    \"\"\"\n", "    # Connect to the SQLite database\n", "    conn = sqlite3.connect(DB_PATH)\n", "    \n", "    try:\n", "        # Read the WorkOrder table into a pandas DataFrame\n", "        query = \"SELECT * FROM WorkOrder LIMIT ?\"\n", "        df = pd.read_sql_query(query, conn, params=(n,))\n", "        \n", "        # Print the DataFrame\n", "        print(f\"\\nFirst {n} rows of WorkOrder table:\")\n", "        \n", "        # Create a styled version for better display in notebook\n", "        styled_df = df.style.set_properties(**{\n", "            'text-align': 'left',\n", "            'white-space': 'pre-wrap',\n", "            'font-size': '10pt'\n", "        }).set_table_styles([\n", "            {'selector': 'th', 'props': [('text-align', 'left'), ('font-size', '10pt')]},\n", "        ])\n", "        \n", "        display(styled_df)\n", "        \n", "    except Exception as e:\n", "        print(f\"Error: {e}\")\n", "        \n", "    finally:\n", "        # Close the connection\n", "        conn.close()\n", "\n", "# Print the first 5 rows of the WorkOrder table\n", "print_workorder_head()"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Column 'crew' added, populated, and cleaned successfully.\n", "\n", "First 5 rows of WorkOrder table:\n"]}, {"data": {"text/html": ["<style type=\"text/css\">\n", "#T_557ce th {\n", "  text-align: left;\n", "  font-size: 10pt;\n", "}\n", "#T_557ce_row0_col0, #T_557ce_row0_col1, #T_557ce_row0_col2, #T_557ce_row0_col3, #T_557ce_row0_col4, #T_557ce_row0_col5, #T_557ce_row0_col6, #T_557ce_row0_col7, #T_557ce_row0_col8, #T_557ce_row0_col9, #T_557ce_row0_col10, #T_557ce_row0_col11, #T_557ce_row0_col12, #T_557ce_row0_col13, #T_557ce_row0_col14, #T_557ce_row0_col15, #T_557ce_row0_col16, #T_557ce_row0_col17, #T_557ce_row0_col18, #T_557ce_row0_col19, #T_557ce_row0_col20, #T_557ce_row1_col0, #T_557ce_row1_col1, #T_557ce_row1_col2, #T_557ce_row1_col3, #T_557ce_row1_col4, #T_557ce_row1_col5, #T_557ce_row1_col6, #T_557ce_row1_col7, #T_557ce_row1_col8, #T_557ce_row1_col9, #T_557ce_row1_col10, #T_557ce_row1_col11, #T_557ce_row1_col12, #T_557ce_row1_col13, #T_557ce_row1_col14, #T_557ce_row1_col15, #T_557ce_row1_col16, #T_557ce_row1_col17, #T_557ce_row1_col18, #T_557ce_row1_col19, #T_557ce_row1_col20, #T_557ce_row2_col0, #T_557ce_row2_col1, #T_557ce_row2_col2, #T_557ce_row2_col3, #T_557ce_row2_col4, #T_557ce_row2_col5, #T_557ce_row2_col6, #T_557ce_row2_col7, #T_557ce_row2_col8, #T_557ce_row2_col9, #T_557ce_row2_col10, #T_557ce_row2_col11, #T_557ce_row2_col12, #T_557ce_row2_col13, #T_557ce_row2_col14, #T_557ce_row2_col15, #T_557ce_row2_col16, #T_557ce_row2_col17, #T_557ce_row2_col18, #T_557ce_row2_col19, #T_557ce_row2_col20, #T_557ce_row3_col0, #T_557ce_row3_col1, #T_557ce_row3_col2, #T_557ce_row3_col3, #T_557ce_row3_col4, #T_557ce_row3_col5, #T_557ce_row3_col6, #T_557ce_row3_col7, #T_557ce_row3_col8, #T_557ce_row3_col9, #T_557ce_row3_col10, #T_557ce_row3_col11, #T_557ce_row3_col12, #T_557ce_row3_col13, #T_557ce_row3_col14, #T_557ce_row3_col15, #T_557ce_row3_col16, #T_557ce_row3_col17, #T_557ce_row3_col18, #T_557ce_row3_col19, #T_557ce_row3_col20, #T_557ce_row4_col0, #T_557ce_row4_col1, #T_557ce_row4_col2, #T_557ce_row4_col3, #T_557ce_row4_col4, #T_557ce_row4_col5, #T_557ce_row4_col6, #T_557ce_row4_col7, #T_557ce_row4_col8, #T_557ce_row4_col9, #T_557ce_row4_col10, #T_557ce_row4_col11, #T_557ce_row4_col12, #T_557ce_row4_col13, #T_557ce_row4_col14, #T_557ce_row4_col15, #T_557ce_row4_col16, #T_557ce_row4_col17, #T_557ce_row4_col18, #T_557ce_row4_col19, #T_557ce_row4_col20 {\n", "  text-align: left;\n", "  white-space: pre-wrap;\n", "  font-size: 10pt;\n", "}\n", "</style>\n", "<table id=\"T_557ce\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_557ce_level0_col0\" class=\"col_heading level0 col0\" >id</th>\n", "      <th id=\"T_557ce_level0_col1\" class=\"col_heading level0 col1\" >site</th>\n", "      <th id=\"T_557ce_level0_col2\" class=\"col_heading level0 col2\" >process</th>\n", "      <th id=\"T_557ce_level0_col3\" class=\"col_heading level0 col3\" >factory</th>\n", "      <th id=\"T_557ce_level0_col4\" class=\"col_heading level0 col4\" >owning_department</th>\n", "      <th id=\"T_557ce_level0_col5\" class=\"col_heading level0 col5\" >location</th>\n", "      <th id=\"T_557ce_level0_col6\" class=\"col_heading level0 col6\" >equipment_id</th>\n", "      <th id=\"T_557ce_level0_col7\" class=\"col_heading level0 col7\" >equipment</th>\n", "      <th id=\"T_557ce_level0_col8\" class=\"col_heading level0 col8\" >work_order_number</th>\n", "      <th id=\"T_557ce_level0_col9\" class=\"col_heading level0 col9\" >stage</th>\n", "      <th id=\"T_557ce_level0_col10\" class=\"col_heading level0 col10\" >description</th>\n", "      <th id=\"T_557ce_level0_col11\" class=\"col_heading level0 col11\" >work_order_type</th>\n", "      <th id=\"T_557ce_level0_col12\" class=\"col_heading level0 col12\" >status</th>\n", "      <th id=\"T_557ce_level0_col13\" class=\"col_heading level0 col13\" >assigned_to</th>\n", "      <th id=\"T_557ce_level0_col14\" class=\"col_heading level0 col14\" >operation_description</th>\n", "      <th id=\"T_557ce_level0_col15\" class=\"col_heading level0 col15\" >operation_result</th>\n", "      <th id=\"T_557ce_level0_col16\" class=\"col_heading level0 col16\" >operation_comments</th>\n", "      <th id=\"T_557ce_level0_col17\" class=\"col_heading level0 col17\" >changed_by</th>\n", "      <th id=\"T_557ce_level0_col18\" class=\"col_heading level0 col18\" >status_date</th>\n", "      <th id=\"T_557ce_level0_col19\" class=\"col_heading level0 col19\" >activity_number</th>\n", "      <th id=\"T_557ce_level0_col20\" class=\"col_heading level0 col20\" >crew</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_557ce_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_557ce_row0_col0\" class=\"data row0 col0\" >1</td>\n", "      <td id=\"T_557ce_row0_col1\" class=\"data row0 col1\" >UTIL.GM</td>\n", "      <td id=\"T_557ce_row0_col2\" class=\"data row0 col2\" >Utility</td>\n", "      <td id=\"T_557ce_row0_col3\" class=\"data row0 col3\" >GM1 COMMON</td>\n", "      <td id=\"T_557ce_row0_col4\" class=\"data row0 col4\" >Electrical</td>\n", "      <td id=\"T_557ce_row0_col5\" class=\"data row0 col5\" >100</td>\n", "      <td id=\"T_557ce_row0_col6\" class=\"data row0 col6\" >U1UBCU00101-002-001</td>\n", "      <td id=\"T_557ce_row0_col7\" class=\"data row0 col7\" >BCU-100 ?-1002 ?-FILTER</td>\n", "      <td id=\"T_557ce_row0_col8\" class=\"data row0 col8\" >GM7057977</td>\n", "      <td id=\"T_557ce_row0_col9\" class=\"data row0 col9\" >Electrical Stage 1</td>\n", "      <td id=\"T_557ce_row0_col10\" class=\"data row0 col10\" >Assembly and Installation Of Parts</td>\n", "      <td id=\"T_557ce_row0_col11\" class=\"data row0 col11\" >PP</td>\n", "      <td id=\"T_557ce_row0_col12\" class=\"data row0 col12\" >DRAFT</td>\n", "      <td id=\"T_557ce_row0_col13\" class=\"data row0 col13\" >Koby Wear</td>\n", "      <td id=\"T_557ce_row0_col14\" class=\"data row0 col14\" >Electrical Stage 1: Observing assembly and installation of parts</td>\n", "      <td id=\"T_557ce_row0_col15\" class=\"data row0 col15\" >10 : OK</td>\n", "      <td id=\"T_557ce_row0_col16\" class=\"data row0 col16\" >TEST</td>\n", "      <td id=\"T_557ce_row0_col17\" class=\"data row0 col17\" ><PERSON></td>\n", "      <td id=\"T_557ce_row0_col18\" class=\"data row0 col18\" >2023-01-01 00:00:00</td>\n", "      <td id=\"T_557ce_row0_col19\" class=\"data row0 col19\" >A00208147</td>\n", "      <td id=\"T_557ce_row0_col20\" class=\"data row0 col20\" >D</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_557ce_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_557ce_row1_col0\" class=\"data row1 col0\" >2</td>\n", "      <td id=\"T_557ce_row1_col1\" class=\"data row1 col1\" >UTIL.GM</td>\n", "      <td id=\"T_557ce_row1_col2\" class=\"data row1 col2\" >Utility</td>\n", "      <td id=\"T_557ce_row1_col3\" class=\"data row1 col3\" >GM1 COMMON</td>\n", "      <td id=\"T_557ce_row1_col4\" class=\"data row1 col4\" >Electrical</td>\n", "      <td id=\"T_557ce_row1_col5\" class=\"data row1 col5\" >100</td>\n", "      <td id=\"T_557ce_row1_col6\" class=\"data row1 col6\" >U1UBCU00101-002-001</td>\n", "      <td id=\"T_557ce_row1_col7\" class=\"data row1 col7\" >BCU-100 ?-1002 ?-FILTER</td>\n", "      <td id=\"T_557ce_row1_col8\" class=\"data row1 col8\" >GM7057977</td>\n", "      <td id=\"T_557ce_row1_col9\" class=\"data row1 col9\" >Electrical Stage 1</td>\n", "      <td id=\"T_557ce_row1_col10\" class=\"data row1 col10\" >Assembly and Installation Of Parts</td>\n", "      <td id=\"T_557ce_row1_col11\" class=\"data row1 col11\" >PP</td>\n", "      <td id=\"T_557ce_row1_col12\" class=\"data row1 col12\" >INPRG</td>\n", "      <td id=\"T_557ce_row1_col13\" class=\"data row1 col13\" >Koby Wear</td>\n", "      <td id=\"T_557ce_row1_col14\" class=\"data row1 col14\" >Electrical Stage 1: Observing assembly and installation of parts</td>\n", "      <td id=\"T_557ce_row1_col15\" class=\"data row1 col15\" >11 : OK</td>\n", "      <td id=\"T_557ce_row1_col16\" class=\"data row1 col16\" >TEST</td>\n", "      <td id=\"T_557ce_row1_col17\" class=\"data row1 col17\" ><PERSON></td>\n", "      <td id=\"T_557ce_row1_col18\" class=\"data row1 col18\" >2023-01-02 23:55:54.992000</td>\n", "      <td id=\"T_557ce_row1_col19\" class=\"data row1 col19\" >A00208147</td>\n", "      <td id=\"T_557ce_row1_col20\" class=\"data row1 col20\" >D</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_557ce_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_557ce_row2_col0\" class=\"data row2 col0\" >3</td>\n", "      <td id=\"T_557ce_row2_col1\" class=\"data row2 col1\" >UTIL.GM</td>\n", "      <td id=\"T_557ce_row2_col2\" class=\"data row2 col2\" >Utility</td>\n", "      <td id=\"T_557ce_row2_col3\" class=\"data row2 col3\" >GM1 COMMON</td>\n", "      <td id=\"T_557ce_row2_col4\" class=\"data row2 col4\" >Electrical</td>\n", "      <td id=\"T_557ce_row2_col5\" class=\"data row2 col5\" >100</td>\n", "      <td id=\"T_557ce_row2_col6\" class=\"data row2 col6\" >U1UBCU00101-002-001</td>\n", "      <td id=\"T_557ce_row2_col7\" class=\"data row2 col7\" >BCU-100 ?-1002 ?-FILTER</td>\n", "      <td id=\"T_557ce_row2_col8\" class=\"data row2 col8\" >GM7057977</td>\n", "      <td id=\"T_557ce_row2_col9\" class=\"data row2 col9\" >Electrical Stage 1</td>\n", "      <td id=\"T_557ce_row2_col10\" class=\"data row2 col10\" >Assembly and Installation Of Parts</td>\n", "      <td id=\"T_557ce_row2_col11\" class=\"data row2 col11\" >PP</td>\n", "      <td id=\"T_557ce_row2_col12\" class=\"data row2 col12\" >OPCOMP</td>\n", "      <td id=\"T_557ce_row2_col13\" class=\"data row2 col13\" >Koby Wear</td>\n", "      <td id=\"T_557ce_row2_col14\" class=\"data row2 col14\" >Electrical Stage 1: Observing assembly and installation of parts</td>\n", "      <td id=\"T_557ce_row2_col15\" class=\"data row2 col15\" >12 : OK</td>\n", "      <td id=\"T_557ce_row2_col16\" class=\"data row2 col16\" >TEST</td>\n", "      <td id=\"T_557ce_row2_col17\" class=\"data row2 col17\" ><PERSON></td>\n", "      <td id=\"T_557ce_row2_col18\" class=\"data row2 col18\" >2023-01-04 02:35:28.589000</td>\n", "      <td id=\"T_557ce_row2_col19\" class=\"data row2 col19\" >A00208147</td>\n", "      <td id=\"T_557ce_row2_col20\" class=\"data row2 col20\" >D</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_557ce_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_557ce_row3_col0\" class=\"data row3 col0\" >4</td>\n", "      <td id=\"T_557ce_row3_col1\" class=\"data row3 col1\" >UTIL.GM</td>\n", "      <td id=\"T_557ce_row3_col2\" class=\"data row3 col2\" >Utility</td>\n", "      <td id=\"T_557ce_row3_col3\" class=\"data row3 col3\" >GM1 COMMON</td>\n", "      <td id=\"T_557ce_row3_col4\" class=\"data row3 col4\" >Electrical</td>\n", "      <td id=\"T_557ce_row3_col5\" class=\"data row3 col5\" >100</td>\n", "      <td id=\"T_557ce_row3_col6\" class=\"data row3 col6\" >U1UBCU00101-002-001</td>\n", "      <td id=\"T_557ce_row3_col7\" class=\"data row3 col7\" >BCU-100 ?-1002 ?-FILTER</td>\n", "      <td id=\"T_557ce_row3_col8\" class=\"data row3 col8\" >GM7057977</td>\n", "      <td id=\"T_557ce_row3_col9\" class=\"data row3 col9\" >Electrical Stage 1</td>\n", "      <td id=\"T_557ce_row3_col10\" class=\"data row3 col10\" >Assembly and Installation Of Parts</td>\n", "      <td id=\"T_557ce_row3_col11\" class=\"data row3 col11\" >PP</td>\n", "      <td id=\"T_557ce_row3_col12\" class=\"data row3 col12\" >COMP</td>\n", "      <td id=\"T_557ce_row3_col13\" class=\"data row3 col13\" >Koby Wear</td>\n", "      <td id=\"T_557ce_row3_col14\" class=\"data row3 col14\" >Electrical Stage 1: Observing assembly and installation of parts</td>\n", "      <td id=\"T_557ce_row3_col15\" class=\"data row3 col15\" >13 : OK</td>\n", "      <td id=\"T_557ce_row3_col16\" class=\"data row3 col16\" >TEST</td>\n", "      <td id=\"T_557ce_row3_col17\" class=\"data row3 col17\" ><PERSON></td>\n", "      <td id=\"T_557ce_row3_col18\" class=\"data row3 col18\" >2023-01-06 14:16:31.017000</td>\n", "      <td id=\"T_557ce_row3_col19\" class=\"data row3 col19\" >A00208147</td>\n", "      <td id=\"T_557ce_row3_col20\" class=\"data row3 col20\" >D</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_557ce_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_557ce_row4_col0\" class=\"data row4 col0\" >5</td>\n", "      <td id=\"T_557ce_row4_col1\" class=\"data row4 col1\" >UTIL.GM</td>\n", "      <td id=\"T_557ce_row4_col2\" class=\"data row4 col2\" >Utility</td>\n", "      <td id=\"T_557ce_row4_col3\" class=\"data row4 col3\" >GM1 COMMON</td>\n", "      <td id=\"T_557ce_row4_col4\" class=\"data row4 col4\" >Electrical</td>\n", "      <td id=\"T_557ce_row4_col5\" class=\"data row4 col5\" >100</td>\n", "      <td id=\"T_557ce_row4_col6\" class=\"data row4 col6\" >U1UBCU00101-002-001</td>\n", "      <td id=\"T_557ce_row4_col7\" class=\"data row4 col7\" >BCU-100 ?-1002 ?-FILTER</td>\n", "      <td id=\"T_557ce_row4_col8\" class=\"data row4 col8\" >GM7057977</td>\n", "      <td id=\"T_557ce_row4_col9\" class=\"data row4 col9\" >Electrical Stage 1</td>\n", "      <td id=\"T_557ce_row4_col10\" class=\"data row4 col10\" >Assembly and Installation Of Parts</td>\n", "      <td id=\"T_557ce_row4_col11\" class=\"data row4 col11\" >PP</td>\n", "      <td id=\"T_557ce_row4_col12\" class=\"data row4 col12\" >CLOSE</td>\n", "      <td id=\"T_557ce_row4_col13\" class=\"data row4 col13\" >Koby Wear</td>\n", "      <td id=\"T_557ce_row4_col14\" class=\"data row4 col14\" >Electrical Stage 1: Observing assembly and installation of parts</td>\n", "      <td id=\"T_557ce_row4_col15\" class=\"data row4 col15\" >14 : OK</td>\n", "      <td id=\"T_557ce_row4_col16\" class=\"data row4 col16\" >TEST</td>\n", "      <td id=\"T_557ce_row4_col17\" class=\"data row4 col17\" ><PERSON></td>\n", "      <td id=\"T_557ce_row4_col18\" class=\"data row4 col18\" >2023-01-09 04:50:21.371000</td>\n", "      <td id=\"T_557ce_row4_col19\" class=\"data row4 col19\" >A00208147</td>\n", "      <td id=\"T_557ce_row4_col20\" class=\"data row4 col20\" >D</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x1f63ab05490>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import os\n", "import sqlite3\n", "import pandas as pd\n", "\n", "DB_PATH = os.path.join('Data', 'Data.db')\n", "\n", "def add_crew_column_to_workorder():\n", "    \"\"\"\n", "    Adds a 'crew' column to the WorkOrder table by joining with <PERSON><PERSON><PERSON><PERSON> on assigned_to = name,\n", "    populates it with OrgChart.shift, and removes '-Crew' from the crew names.\n", "    \"\"\"\n", "    conn = sqlite3.connect(DB_PATH)\n", "    cursor = conn.cursor()\n", "\n", "    try:\n", "        # Check if 'crew' column already exists\n", "        cursor.execute(\"PRAGMA table_info(WorkOrder)\")\n", "        columns = [col[1] for col in cursor.fetchall()]\n", "        if 'crew' not in columns:\n", "            cursor.execute(\"ALTER TABLE WorkOrder ADD COLUMN crew TEXT\")\n", "\n", "        # Populate the 'crew' column via a join\n", "        update_query = \"\"\"\n", "        UPDATE WorkOrder\n", "        SET crew = (\n", "            SELECT OrgChart.shift\n", "            FROM OrgChart\n", "            WHERE OrgChart.name = WorkOrder.assigned_to\n", "        )\n", "        WHERE assigned_to IN (\n", "            SELECT name FROM OrgChart\n", "        )\n", "        \"\"\"\n", "        cursor.execute(update_query)\n", "\n", "        # Remove '-Crew' from the crew values\n", "        clean_query = \"\"\"\n", "        UPDATE WorkOrder\n", "        SET crew = REPLACE(crew, '-Crew', '')\n", "        WHERE crew LIKE '%-Crew'\n", "        \"\"\"\n", "        cursor.execute(clean_query)\n", "\n", "        conn.commit()\n", "        print(\"Column 'crew' added, populated, and cleaned successfully.\")\n", "\n", "    finally:\n", "        conn.close()\n", "\n", "add_crew_column_to_workorder()\n", "print_workorder_head()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.6"}}, "nbformat": 4, "nbformat_minor": 2}