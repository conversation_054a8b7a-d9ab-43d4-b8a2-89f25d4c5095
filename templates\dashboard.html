<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Ultium Cells</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='sidebar.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@24,400,0,0">
    
    <style>
        .dashboard-content {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .welcome-section {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .welcome-section h1 {
            color: #151A2D;
            margin-bottom: 10px;
        }
        
        .welcome-section p {
            color: #666;
            font-size: 1.1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-card h3 {
            margin: 0 0 10px 0;
            font-size: 2rem;
        }
        
        .stat-card p {
            margin: 0;
            opacity: 0.9;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 30px;
        }
        
        .action-btn {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            text-decoration: none;
            color: #495057;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            background: #e9ecef;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        .action-btn .material-symbols-rounded {
            margin-right: 10px;
            font-size: 1.5rem;
        }
    </style>
</head>
<body>
    {% include 'sidebar.html' %}
    
    <div class="container">
        <div class="dashboard-content">
            <div class="welcome-section">
                <h1>Welcome to Ultium Cells Dashboard</h1>
                <p>Manage your facility operations, track work orders, and monitor performance.</p>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>24</h3>
                    <p>Active Work Orders</p>
                </div>
                <div class="stat-card">
                    <h3>12</h3>
                    <p>Completed Today</p>
                </div>
                <div class="stat-card">
                    <h3>3</h3>
                    <p>Pending Approvals</p>
                </div>
                <div class="stat-card">
                    <h3>98%</h3>
                    <p>System Uptime</p>
                </div>
            </div>
            
            <h2 style="margin-top: 40px; margin-bottom: 20px; color: #151A2D;">Quick Actions</h2>
            <div class="quick-actions">
                <a href="{{ url_for('calendar') }}" class="action-btn">
                    <span class="material-symbols-rounded">calendar_today</span>
                    View Calendar
                </a>
                <a href="{{ url_for('analytics') }}" class="action-btn">
                    <span class="material-symbols-rounded">insert_chart</span>
                    Gantt Chart
                </a>
                <a href="{{ url_for('training') }}" class="action-btn">
                    <span class="material-symbols-rounded">school</span>
                    Training Records
                </a>
                <a href="{{ url_for('organization_chart') }}" class="action-btn">
                    <span class="material-symbols-rounded">account_tree</span>
                    Organization Chart
                </a>
                <a href="{{ url_for('authorization') }}" class="action-btn">
                    <span class="material-symbols-rounded">key</span>
                    User Authorization
                </a>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='sidebar.js') }}"></script>
</body>
</html>
