import os
import sqlite3
import pandas as pd
import sys
import logging

# Set up logging
logging.basicConfig(
    filename='excel_script.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

try:
    # Define paths
    logging.info("Starting script")
    data_dir = 'data'
    db_path = os.path.join(data_dir, 'Data.db')
    output_path = os.path.join(data_dir, 'Facility Flex Chart LOCAL.xlsx')
    
    logging.info(f"Database path: {db_path}")
    logging.info(f"Output path: {output_path}")
    
    # Create data directory if it doesn't exist
    os.makedirs(data_dir, exist_ok=True)
    logging.info("Data directory created/verified")
    
    # Connect to the database
    logging.info("Connecting to database")
    conn = sqlite3.connect(db_path)
    
    # Get the data from the WorkOrder table
    logging.info("Retrieving data from WorkOrder table")
    df = pd.read_sql_query("SELECT * FROM WorkOrder", conn)
    logging.info(f"Retrieved {len(df)} rows from WorkOrder table")
    
    # Close the database connection
    conn.close()
    logging.info("Database connection closed")
    
    # Create the Excel file
    logging.info("Creating Excel file")
    df.to_excel(output_path, sheet_name='AppData', index=False)
    
    logging.info(f"Excel file created successfully: {output_path}")
    
except Exception as e:
    logging.error(f"Error: {str(e)}")
    sys.exit(1)

logging.info("Script completed successfully")
sys.exit(0)
