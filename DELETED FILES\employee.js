document.addEventListener('DOMContentLoaded', function() {
    // Get references to DOM elements
    const employeeTable = document.getElementById('employeeTable').getElementsByTagName('tbody')[0];
    const departmentInput = document.getElementById('department');
    const employeeNameInput = document.getElementById('employeeName');
    const tradeInput = document.getElementById('trade');
    const crewInput = document.getElementById('crew');
    const startDateInput = document.getElementById('startDate');
    const addEmployeeBtn = document.getElementById('addEmployeeBtn');

    // Get filter input elements
    const departmentFilter = document.getElementById('departmentFilter');
    const nameFilter = document.getElementById('nameFilter');
    const tradeFilter = document.getElementById('tradeFilter');
    const crewFilter = document.getElementById('crewFilter');
    const dateFilter = document.getElementById('dateFilter');

    // Add event listener for add button click
    addEmployeeBtn.addEventListener('click', function() {
        // Get input values
        const department = departmentInput.value.trim();
        const employeeName = employeeNameInput.value.trim();
        const trade = tradeInput.value.trim();
        const crew = crewInput.value.trim();
        const startDate = startDateInput.value.trim();

        // Validate inputs
        if (department === '' || employeeName === '' || trade === '' || crew === '' || startDate === '') {
            alert('Please fill in all fields');
            return;
        }

        // Create employee data object
        const employeeData = {
            department: department,
            name: employeeName,
            trade: trade,
            crew: crew,
            start_date: startDate
        };

        // Send data to the server
        fetch('/api/employees', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(employeeData),
        })
        .then(response => {
            if (response.status === 409) {
                // Employee already exists
                alert('This employee already exists with the same trade and crew!');
                throw new Error('Employee already exists');
            }
            return response.json();
        })
        .then(data => {
            // Add the new employee to the table
            addEmployeeRow(data.id, data.department, data.name, data.trade, data.crew, data.start_date);

            // Clear the inputs
            departmentInput.value = '';
            employeeNameInput.value = '';
            tradeInput.value = '';
            crewInput.value = '';
            startDateInput.value = '';

            // Focus on the first input for next entry
            departmentInput.focus();
        })
        .catch(error => {
            console.error('Error:', error);
        });
    });

    // Function to add a new row to the table
    function addEmployeeRow(id, department, name, trade, crew, startDate) {
        // Create a new row
        const newRow = employeeTable.insertRow();
        newRow.setAttribute('data-id', id);

        // Insert cells for each column
        const departmentCell = newRow.insertCell(0);
        const nameCell = newRow.insertCell(1);
        const tradeCell = newRow.insertCell(2);
        const crewCell = newRow.insertCell(3);
        const startDateCell = newRow.insertCell(4);
        const actionsCell = newRow.insertCell(5);

        // Add text to cells
        departmentCell.textContent = department;
        nameCell.textContent = name;
        tradeCell.textContent = trade;
        crewCell.textContent = crew;
        startDateCell.textContent = startDate;

        // Create delete button
        const deleteButton = document.createElement('button');
        deleteButton.textContent = 'Delete';
        deleteButton.className = 'btn-delete';
        deleteButton.onclick = function() { deleteEmployee(id); };

        // Add delete button to actions cell
        actionsCell.appendChild(deleteButton);
    }

    // Function to delete an employee
    window.deleteEmployee = function(id) {
        if (confirm('Are you sure you want to delete this employee?')) {
            fetch(`/api/employees/${id}`, {
                method: 'DELETE',
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to delete employee');
                }
                return response.json();
            })
            .then(() => {
                // Remove the row from the table
                const row = document.querySelector(`tr[data-id="${id}"]`);
                if (row) {
                    row.remove();
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Failed to delete employee. Please try again.');
            });
        }
    };

    // Filter functionality
    function filterTable() {
        const departmentValue = departmentFilter.value.toLowerCase();
        const nameValue = nameFilter.value.toLowerCase();
        const tradeValue = tradeFilter.value.toLowerCase();
        const crewValue = crewFilter.value.toLowerCase();
        const dateValue = dateFilter.value.toLowerCase();

        // Get all rows in the table body
        const rows = employeeTable.getElementsByTagName('tr');

        // Loop through all table rows
        for (let i = 0; i < rows.length; i++) {
            const departmentCell = rows[i].getElementsByTagName('td')[0];
            const nameCell = rows[i].getElementsByTagName('td')[1];
            const tradeCell = rows[i].getElementsByTagName('td')[2];
            const crewCell = rows[i].getElementsByTagName('td')[3];
            const dateCell = rows[i].getElementsByTagName('td')[4];

            if (departmentCell && nameCell && tradeCell && crewCell && dateCell) {
                const departmentText = departmentCell.textContent.toLowerCase();
                const nameText = nameCell.textContent.toLowerCase();
                const tradeText = tradeCell.textContent.toLowerCase();
                const crewText = crewCell.textContent.toLowerCase();
                const dateText = dateCell.textContent.toLowerCase();

                // Check if the row should be displayed based on all filters
                const departmentMatch = departmentText.includes(departmentValue);
                const nameMatch = nameText.includes(nameValue);
                const tradeMatch = tradeText.includes(tradeValue);
                const crewMatch = crewText.includes(crewValue);
                const dateMatch = dateText.includes(dateValue);

                if (departmentMatch && nameMatch && tradeMatch && crewMatch && dateMatch) {
                    rows[i].style.display = '';
                } else {
                    rows[i].style.display = 'none';
                }
            }
        }
    }

    // Add event listeners to all filter inputs
    departmentFilter.addEventListener('input', filterTable);
    nameFilter.addEventListener('input', filterTable);
    tradeFilter.addEventListener('input', filterTable);
    crewFilter.addEventListener('input', filterTable);
    dateFilter.addEventListener('input', filterTable);
});