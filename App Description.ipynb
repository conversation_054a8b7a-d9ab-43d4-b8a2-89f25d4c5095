{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ultium Cells Facility Management Application - Flex Chart\n", "\n", "## Project.py\n", "\n", "The `project.py` file is the main entry point for the Flask application. It contains the following key components:\n", "\n", "### Flask Application Setup\n", "\n", "The Flask application is initialized with the following line:\n", "\n", "```python\n", "app = Flask(__name__)\n", "```\n", "\n", "The Database is created and connected to the application with the following line:\n", "\n", "```python\n", "DB_PATH = os.path.join('data', 'Data.db')\n", "os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Database Structure\n", "\n", "### Check if the database file exists"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Database path: c:\\Users\\<USER>\\Desktop\\MyWebsite\\UltiumApp\\Data\\Data.db\n", "\n", "Database file exists: Data\\Data.db\n"]}], "source": ["import sqlite3\n", "import os\n", "\n", "# Database path - make sure this matches the path in project.py\n", "DB_PATH = os.path.join('Data', 'Data.db')\n", "print(f\"Database path: {os.path.abspath(DB_PATH)}\")\n", "\n", "# Check if the database file exists\n", "if os.path.exists(DB_PATH):\n", "    print(f\"\\nDatabase file exists: {DB_PATH}\")\n", "else:\n", "    print(f\"Database file does not exist: {DB_PATH}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### List the tables in the database and their columns"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tables in the database:\n", "  sqlite_sequence\n", "  WorkOrder_temp\n", "  Authorization\n", "  AppData\n", "  WorkOrder\n", "  <PERSON><PERSON><PERSON><PERSON>\n", "\n", "WorkOrder table columns:\n", "id, site, process, factory, owning_department, location, equipment_id, equipment, work_order_number, stage, description, work_order_type, status, assigned_to, operation_description, operation_result, operation_comments, changed_by, status_date, activity_number\n"]}], "source": ["def print_database_schema():\n", "    # Connect to the SQLite database\n", "    conn = sqlite3.connect(DB_PATH)\n", "    cursor = conn.cursor()\n", "    \n", "    # Get list of tables\n", "    cursor.execute(\"SELECT name FROM sqlite_master WHERE type='table';\")\n", "    tables = cursor.fetchall()\n", "    \n", "    print(\"Tables in the database:\")\n", "    for table in tables:\n", "        print(f\"  {table[0]}\")\n", "    \n", "    # Close the connection\n", "    conn.close()\n", "\n", "# Print the database schema\n", "print_database_schema()\n", "table_choice = 'WorkOrder'\n", "\n", "def print_table_schema():\n", "    # Connect to the SQLite database\n", "    conn = sqlite3.connect(DB_PATH)\n", "    cursor = conn.cursor()\n", "    \n", "    try:\n", "        print(f\"\\n{table_choice} table columns:\")\n", "        cursor.execute(f\"PRAGMA table_info('{table_choice}')\")\n", "        schema = cursor.fetchall()\n", "        \n", "        if not schema:\n", "            print(\"No columns found or table does not exist.\")\n", "            return\n", "\n", "        # Extract and print column names in comma-separated format\n", "        column_names = [column[1] for column in schema]\n", "        print(\", \".join(column_names))\n", "        \n", "    except sqlite3.OperationalError as e:\n", "        print(f\"Error: {e}\")\n", "        \n", "    finally:\n", "        conn.close()\n", "\n", "# Print the table columns\n", "print_table_schema()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Tables in the database:\n", "  \n", "- sqlite_sequence: internal SQLite table that helps implement the AUTOINCREMENT feature.  \n", "- WorkOrder_temp: Empty table with same schema as WorkOrder  \n", "- Authorization: List of users and their access level\n", "- AppData: Data points to export to Flex Chart (Getting the Latestest status for each work order)\n", "- OrgChart: Organization Chart. This data is also exported to the Flex Chart\n", "- WorkOrder: List of Work Orders and Status History for Gantt Chart\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Print head of Table"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "First 20 rows of WorkOrder table:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>site</th>\n", "      <th>process</th>\n", "      <th>factory</th>\n", "      <th>owning_department</th>\n", "      <th>location</th>\n", "      <th>equipment_id</th>\n", "      <th>equipment</th>\n", "      <th>work_order_number</th>\n", "      <th>stage</th>\n", "      <th>description</th>\n", "      <th>work_order_type</th>\n", "      <th>status</th>\n", "      <th>assigned_to</th>\n", "      <th>operation_description</th>\n", "      <th>operation_result</th>\n", "      <th>operation_comments</th>\n", "      <th>changed_by</th>\n", "      <th>status_date</th>\n", "      <th>activity_number</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>UTIL.GM</td>\n", "      <td>UTILITY</td>\n", "      <td>GM1 COMMON</td>\n", "      <td>Boiler</td>\n", "      <td>100</td>\n", "      <td>U1UBCU00401-003-001</td>\n", "      <td>BCU-100 ?-2011 ?-FILTER</td>\n", "      <td>GM7058313</td>\n", "      <td>Safety Stage 1</td>\n", "      <td>Blowdowns</td>\n", "      <td>PP</td>\n", "      <td>DRAFT</td>\n", "      <td><PERSON></td>\n", "      <td>Boiler Stage 1: Observe Blowdowns (BH-003)</td>\n", "      <td>2830 : OK</td>\n", "      <td>TEST</td>\n", "      <td><PERSON></td>\n", "      <td>2025-02-06 10:15:20.960000</td>\n", "      <td>A00208319</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>UTIL.GM</td>\n", "      <td>UTILITY</td>\n", "      <td>GM1 COMMON</td>\n", "      <td>Boiler</td>\n", "      <td>100</td>\n", "      <td>U1UBCU00401-003-001</td>\n", "      <td>BCU-100 ?-2011 ?-FILTER</td>\n", "      <td>GM7058313</td>\n", "      <td>Safety Stage 1</td>\n", "      <td>Blowdowns</td>\n", "      <td>PP</td>\n", "      <td>INPRG</td>\n", "      <td><PERSON></td>\n", "      <td>Boiler Stage 1: Observe Blowdowns (BH-003)</td>\n", "      <td>2832 : OK</td>\n", "      <td>TEST</td>\n", "      <td><PERSON></td>\n", "      <td>2025-02-07 23:18:48.684000</td>\n", "      <td>A00208319</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>UTIL.GM</td>\n", "      <td>UTILITY</td>\n", "      <td>GM1 COMMON</td>\n", "      <td>Boiler</td>\n", "      <td>100</td>\n", "      <td>U1UBCU00401-003-001</td>\n", "      <td>BCU-100 ?-2011 ?-FILTER</td>\n", "      <td>GM7058313</td>\n", "      <td>Safety Stage 1</td>\n", "      <td>Blowdowns</td>\n", "      <td>PP</td>\n", "      <td>OPCOMP</td>\n", "      <td><PERSON></td>\n", "      <td>Boiler Stage 1: Observe Blowdowns (BH-003)</td>\n", "      <td>2834 : OK</td>\n", "      <td>TEST</td>\n", "      <td><PERSON></td>\n", "      <td>2025-02-09 16:17:00.632000</td>\n", "      <td>A00208319</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>UTIL.GM</td>\n", "      <td>UTILITY</td>\n", "      <td>GM1 COMMON</td>\n", "      <td>Boiler</td>\n", "      <td>100</td>\n", "      <td>U1UBCU00401-003-001</td>\n", "      <td>BCU-100 ?-2011 ?-FILTER</td>\n", "      <td>GM7058313</td>\n", "      <td>Safety Stage 1</td>\n", "      <td>Blowdowns</td>\n", "      <td>PP</td>\n", "      <td>COMP</td>\n", "      <td><PERSON></td>\n", "      <td>Boiler Stage 1: Observe Blowdowns (BH-003)</td>\n", "      <td>2836 : OK</td>\n", "      <td>TEST</td>\n", "      <td><PERSON></td>\n", "      <td>2025-02-10 05:01:37.497000</td>\n", "      <td>A00208319</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>UTIL.GM</td>\n", "      <td>UTILITY</td>\n", "      <td>GM1 COMMON</td>\n", "      <td>Boiler</td>\n", "      <td>100</td>\n", "      <td>U1UBCU00401-003-001</td>\n", "      <td>BCU-100 ?-2011 ?-FILTER</td>\n", "      <td>GM7058313</td>\n", "      <td>Safety Stage 1</td>\n", "      <td>Blowdowns</td>\n", "      <td>PP</td>\n", "      <td>CLOSE</td>\n", "      <td><PERSON></td>\n", "      <td>Boiler Stage 1: Observe Blowdowns (BH-003)</td>\n", "      <td>2838 : OK</td>\n", "      <td>TEST</td>\n", "      <td><PERSON></td>\n", "      <td>2025-02-11 22:38:55.321000</td>\n", "      <td>A00208319</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>6</td>\n", "      <td>UTIL.GM</td>\n", "      <td>UTILITY</td>\n", "      <td>GM1 COMMON</td>\n", "      <td>Boiler</td>\n", "      <td>100</td>\n", "      <td>U1UBCU00401-003-001</td>\n", "      <td>BCU-100 ?-2011 ?-FILTER</td>\n", "      <td>GM70583135</td>\n", "      <td>Safety Stage 1</td>\n", "      <td>Boiler Adjustments</td>\n", "      <td>PP</td>\n", "      <td>DRAFT</td>\n", "      <td><PERSON></td>\n", "      <td>Boiler Stage 1: Observe Boiler Adjustments (BH...</td>\n", "      <td>2850 : OK</td>\n", "      <td>TEST</td>\n", "      <td><PERSON></td>\n", "      <td>2025-02-23 00:01:06.955000</td>\n", "      <td>A00208320</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>7</td>\n", "      <td>UTIL.GM</td>\n", "      <td>UTILITY</td>\n", "      <td>GM1 COMMON</td>\n", "      <td>Boiler</td>\n", "      <td>100</td>\n", "      <td>U1UBCU00401-003-001</td>\n", "      <td>BCU-100 ?-2011 ?-FILTER</td>\n", "      <td>GM70583135</td>\n", "      <td>Safety Stage 1</td>\n", "      <td>Boiler Adjustments</td>\n", "      <td>PP</td>\n", "      <td>INPRG</td>\n", "      <td><PERSON></td>\n", "      <td>Boiler Stage 1: Observe Boiler Adjustments (BH...</td>\n", "      <td>2852 : OK</td>\n", "      <td>TEST</td>\n", "      <td><PERSON></td>\n", "      <td>2025-02-25 17:52:04.996000</td>\n", "      <td>A00208320</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>8</td>\n", "      <td>UTIL.GM</td>\n", "      <td>UTILITY</td>\n", "      <td>GM1 COMMON</td>\n", "      <td>Boiler</td>\n", "      <td>100</td>\n", "      <td>U1UBCU00401-003-001</td>\n", "      <td>BCU-100 ?-2011 ?-FILTER</td>\n", "      <td>GM70583135</td>\n", "      <td>Safety Stage 1</td>\n", "      <td>Boiler Adjustments</td>\n", "      <td>PP</td>\n", "      <td>OPCOMP</td>\n", "      <td><PERSON></td>\n", "      <td>Boiler Stage 1: Observe Boiler Adjustments (BH...</td>\n", "      <td>2854 : OK</td>\n", "      <td>TEST</td>\n", "      <td><PERSON></td>\n", "      <td>2025-02-27 11:13:14.725000</td>\n", "      <td>A00208320</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>9</td>\n", "      <td>UTIL.GM</td>\n", "      <td>UTILITY</td>\n", "      <td>GM1 COMMON</td>\n", "      <td>Boiler</td>\n", "      <td>100</td>\n", "      <td>U1UBCU00401-003-001</td>\n", "      <td>BCU-100 ?-2011 ?-FILTER</td>\n", "      <td>GM70583135</td>\n", "      <td>Safety Stage 1</td>\n", "      <td>Boiler Adjustments</td>\n", "      <td>PP</td>\n", "      <td>COMP</td>\n", "      <td><PERSON></td>\n", "      <td>Boiler Stage 1: Observe Boiler Adjustments (BH...</td>\n", "      <td>2856 : OK</td>\n", "      <td>TEST</td>\n", "      <td><PERSON></td>\n", "      <td>2025-03-03 12:13:25.045000</td>\n", "      <td>A00208320</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>10</td>\n", "      <td>UTIL.GM</td>\n", "      <td>UTILITY</td>\n", "      <td>GM1 COMMON</td>\n", "      <td>Boiler</td>\n", "      <td>100</td>\n", "      <td>U1UBCU00401-003-001</td>\n", "      <td>BCU-100 ?-2011 ?-FILTER</td>\n", "      <td>GM70583135</td>\n", "      <td>Safety Stage 1</td>\n", "      <td>Boiler Adjustments</td>\n", "      <td>PP</td>\n", "      <td>CLOSE</td>\n", "      <td><PERSON></td>\n", "      <td>Boiler Stage 1: Observe Boiler Adjustments (BH...</td>\n", "      <td>2858 : OK</td>\n", "      <td>TEST</td>\n", "      <td><PERSON></td>\n", "      <td>2025-03-05 06:00:49.984000</td>\n", "      <td>A00208320</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>11</td>\n", "      <td>UTIL.GM</td>\n", "      <td>UTILITY</td>\n", "      <td>GM1 COMMON</td>\n", "      <td>Boiler</td>\n", "      <td>100</td>\n", "      <td>U1UBCU00401-003-001</td>\n", "      <td>BCU-100 ?-2011 ?-FILTER</td>\n", "      <td>GM7058314</td>\n", "      <td>Safety Stage 1</td>\n", "      <td>Boiler System Maintenance Requirements</td>\n", "      <td>PP</td>\n", "      <td>DRAFT</td>\n", "      <td><PERSON></td>\n", "      <td>Boiler Stage 1: Observe Boiler System Maintena...</td>\n", "      <td>2870 : OK</td>\n", "      <td>TEST</td>\n", "      <td>MAXADMIN</td>\n", "      <td>2025-03-23 11:36:25.423000</td>\n", "      <td>A00208326</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>12</td>\n", "      <td>UTIL.GM</td>\n", "      <td>UTILITY</td>\n", "      <td>GM1 COMMON</td>\n", "      <td>Boiler</td>\n", "      <td>100</td>\n", "      <td>U1UBCU00401-003-001</td>\n", "      <td>BCU-100 ?-2011 ?-FILTER</td>\n", "      <td>GM7058314</td>\n", "      <td>Safety Stage 1</td>\n", "      <td>Boiler System Maintenance Requirements</td>\n", "      <td>PP</td>\n", "      <td>INPRG</td>\n", "      <td><PERSON></td>\n", "      <td>Boiler Stage 1: Observe Boiler System Maintena...</td>\n", "      <td>2872 : OK</td>\n", "      <td>TEST</td>\n", "      <td>MAXADMIN</td>\n", "      <td>2025-03-27 22:02:18.121000</td>\n", "      <td>A00208326</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>13</td>\n", "      <td>UTIL.GM</td>\n", "      <td>UTILITY</td>\n", "      <td>GM1 COMMON</td>\n", "      <td>Boiler</td>\n", "      <td>100</td>\n", "      <td>U1UBCU00401-003-001</td>\n", "      <td>BCU-100 ?-2011 ?-FILTER</td>\n", "      <td>GM7058314</td>\n", "      <td>Safety Stage 1</td>\n", "      <td>Boiler System Maintenance Requirements</td>\n", "      <td>PP</td>\n", "      <td>OPCOMP</td>\n", "      <td><PERSON></td>\n", "      <td>Boiler Stage 1: Observe Boiler System Maintena...</td>\n", "      <td>2874 : OK</td>\n", "      <td>TEST</td>\n", "      <td>MAXADMIN</td>\n", "      <td>2025-03-29 04:23:08.360000</td>\n", "      <td>A00208326</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>14</td>\n", "      <td>UTIL.GM</td>\n", "      <td>UTILITY</td>\n", "      <td>GM1 COMMON</td>\n", "      <td>Boiler</td>\n", "      <td>100</td>\n", "      <td>U1UBCU00401-003-001</td>\n", "      <td>BCU-100 ?-2011 ?-FILTER</td>\n", "      <td>GM7058314</td>\n", "      <td>Safety Stage 1</td>\n", "      <td>Boiler System Maintenance Requirements</td>\n", "      <td>PP</td>\n", "      <td>COMP</td>\n", "      <td><PERSON></td>\n", "      <td>Boiler Stage 1: Observe Boiler System Maintena...</td>\n", "      <td>2876 : OK</td>\n", "      <td>TEST</td>\n", "      <td>MAXADMIN</td>\n", "      <td>2025-04-02 01:46:18.878000</td>\n", "      <td>A00208326</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>15</td>\n", "      <td>UTIL.GM</td>\n", "      <td>UTILITY</td>\n", "      <td>GM1 COMMON</td>\n", "      <td>Boiler</td>\n", "      <td>100</td>\n", "      <td>U1UBCU00401-003-001</td>\n", "      <td>BCU-100 ?-2011 ?-FILTER</td>\n", "      <td>GM7058314</td>\n", "      <td>Safety Stage 1</td>\n", "      <td>Boiler System Maintenance Requirements</td>\n", "      <td>PP</td>\n", "      <td>CLOSE</td>\n", "      <td><PERSON></td>\n", "      <td>Boiler Stage 1: Observe Boiler System Maintena...</td>\n", "      <td>2878 : OK</td>\n", "      <td>TEST</td>\n", "      <td>MAXADMIN</td>\n", "      <td>2025-04-04 16:48:36.092000</td>\n", "      <td>A00208326</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>16</td>\n", "      <td>UTIL.GM</td>\n", "      <td>UTILITY</td>\n", "      <td>GM1 COMMON</td>\n", "      <td>Boiler</td>\n", "      <td>100</td>\n", "      <td>U1UBCU00401-003-001</td>\n", "      <td>BCU-100 ?-2011 ?-FILTER</td>\n", "      <td>GM70583145</td>\n", "      <td>Safety Stage 1</td>\n", "      <td>Brix Testing Process</td>\n", "      <td>PP</td>\n", "      <td>DRAFT</td>\n", "      <td><PERSON></td>\n", "      <td>Boiler Stage 1: Observe Brix Testing Process (...</td>\n", "      <td>2890 : OK</td>\n", "      <td>TEST</td>\n", "      <td><PERSON></td>\n", "      <td>2025-04-17 21:07:10.384000</td>\n", "      <td>A00208334</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>17</td>\n", "      <td>UTIL.GM</td>\n", "      <td>UTILITY</td>\n", "      <td>GM1 COMMON</td>\n", "      <td>Boiler</td>\n", "      <td>100</td>\n", "      <td>U1UBCU00401-003-001</td>\n", "      <td>BCU-100 ?-2011 ?-FILTER</td>\n", "      <td>GM70583145</td>\n", "      <td>Safety Stage 1</td>\n", "      <td>Brix Testing Process</td>\n", "      <td>PP</td>\n", "      <td>INPRG</td>\n", "      <td><PERSON></td>\n", "      <td>Boiler Stage 1: Observe Brix Testing Process (...</td>\n", "      <td>2892 : OK</td>\n", "      <td>TEST</td>\n", "      <td><PERSON></td>\n", "      <td>2025-04-19 05:45:20.586000</td>\n", "      <td>A00208334</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>18</td>\n", "      <td>UTIL.GM</td>\n", "      <td>UTILITY</td>\n", "      <td>GM1 COMMON</td>\n", "      <td>Boiler</td>\n", "      <td>100</td>\n", "      <td>U1UBCU00401-003-001</td>\n", "      <td>BCU-100 ?-2011 ?-FILTER</td>\n", "      <td>GM70583145</td>\n", "      <td>Safety Stage 1</td>\n", "      <td>Brix Testing Process</td>\n", "      <td>PP</td>\n", "      <td>OPCOMP</td>\n", "      <td><PERSON></td>\n", "      <td>Boiler Stage 1: Observe Brix Testing Process (...</td>\n", "      <td>2894 : OK</td>\n", "      <td>TEST</td>\n", "      <td><PERSON></td>\n", "      <td>2025-04-21 11:29:18.433000</td>\n", "      <td>A00208334</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>19</td>\n", "      <td>UTIL.GM</td>\n", "      <td>UTILITY</td>\n", "      <td>GM1 COMMON</td>\n", "      <td>Boiler</td>\n", "      <td>100</td>\n", "      <td>U1UBCU00401-003-001</td>\n", "      <td>BCU-100 ?-2011 ?-FILTER</td>\n", "      <td>GM70583145</td>\n", "      <td>Safety Stage 1</td>\n", "      <td>Brix Testing Process</td>\n", "      <td>PP</td>\n", "      <td>COMP</td>\n", "      <td><PERSON></td>\n", "      <td>Boiler Stage 1: Observe Brix Testing Process (...</td>\n", "      <td>2896 : OK</td>\n", "      <td>TEST</td>\n", "      <td><PERSON></td>\n", "      <td>2025-04-23 14:16:02.515000</td>\n", "      <td>A00208334</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>20</td>\n", "      <td>UTIL.GM</td>\n", "      <td>UTILITY</td>\n", "      <td>GM1 COMMON</td>\n", "      <td>Boiler</td>\n", "      <td>100</td>\n", "      <td>U1UBCU00401-003-001</td>\n", "      <td>BCU-100 ?-2011 ?-FILTER</td>\n", "      <td>GM70583145</td>\n", "      <td>Safety Stage 1</td>\n", "      <td>Brix Testing Process</td>\n", "      <td>PP</td>\n", "      <td>CLOSE</td>\n", "      <td><PERSON></td>\n", "      <td>Boiler Stage 1: Observe Brix Testing Process (...</td>\n", "      <td>2898 : OK</td>\n", "      <td>TEST</td>\n", "      <td><PERSON></td>\n", "      <td>2025-04-26 08:53:18.344000</td>\n", "      <td>A00208334</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id     site  process     factory owning_department location  \\\n", "0    1  UTIL.GM  UTILITY  GM1 COMMON            Boiler      100   \n", "1    2  UTIL.GM  UTILITY  GM1 COMMON            Boiler      100   \n", "2    3  UTIL.GM  UTILITY  GM1 COMMON            Boiler      100   \n", "3    4  UTIL.GM  UTILITY  GM1 COMMON            Boiler      100   \n", "4    5  UTIL.GM  UTILITY  GM1 COMMON            Boiler      100   \n", "5    6  UTIL.GM  UTILITY  GM1 COMMON            Boiler      100   \n", "6    7  UTIL.GM  UTILITY  GM1 COMMON            Boiler      100   \n", "7    8  UTIL.GM  UTILITY  GM1 COMMON            Boiler      100   \n", "8    9  UTIL.GM  UTILITY  GM1 COMMON            Boiler      100   \n", "9   10  UTIL.GM  UTILITY  GM1 COMMON            Boiler      100   \n", "10  11  UTIL.GM  UTILITY  GM1 COMMON            Boiler      100   \n", "11  12  UTIL.GM  UTILITY  GM1 COMMON            Boiler      100   \n", "12  13  UTIL.GM  UTILITY  GM1 COMMON            Boiler      100   \n", "13  14  UTIL.GM  UTILITY  GM1 COMMON            Boiler      100   \n", "14  15  UTIL.GM  UTILITY  GM1 COMMON            Boiler      100   \n", "15  16  UTIL.GM  UTILITY  GM1 COMMON            Boiler      100   \n", "16  17  UTIL.GM  UTILITY  GM1 COMMON            Boiler      100   \n", "17  18  UTIL.GM  UTILITY  GM1 COMMON            Boiler      100   \n", "18  19  UTIL.GM  UTILITY  GM1 COMMON            Boiler      100   \n", "19  20  UTIL.GM  UTILITY  GM1 COMMON            Boiler      100   \n", "\n", "           equipment_id                equipment work_order_number  \\\n", "0   U1UBCU00401-003-001  BCU-100 ?-2011 ?-FILTER         GM7058313   \n", "1   U1UBCU00401-003-001  BCU-100 ?-2011 ?-FILTER         GM7058313   \n", "2   U1UBCU00401-003-001  BCU-100 ?-2011 ?-FILTER         GM7058313   \n", "3   U1UBCU00401-003-001  BCU-100 ?-2011 ?-FILTER         GM7058313   \n", "4   U1UBCU00401-003-001  BCU-100 ?-2011 ?-FILTER         GM7058313   \n", "5   U1UBCU00401-003-001  BCU-100 ?-2011 ?-FILTER        GM70583135   \n", "6   U1UBCU00401-003-001  BCU-100 ?-2011 ?-FILTER        GM70583135   \n", "7   U1UBCU00401-003-001  BCU-100 ?-2011 ?-FILTER        GM70583135   \n", "8   U1UBCU00401-003-001  BCU-100 ?-2011 ?-FILTER        GM70583135   \n", "9   U1UBCU00401-003-001  BCU-100 ?-2011 ?-FILTER        GM70583135   \n", "10  U1UBCU00401-003-001  BCU-100 ?-2011 ?-FILTER         GM7058314   \n", "11  U1UBCU00401-003-001  BCU-100 ?-2011 ?-FILTER         GM7058314   \n", "12  U1UBCU00401-003-001  BCU-100 ?-2011 ?-FILTER         GM7058314   \n", "13  U1UBCU00401-003-001  BCU-100 ?-2011 ?-FILTER         GM7058314   \n", "14  U1UBCU00401-003-001  BCU-100 ?-2011 ?-FILTER         GM7058314   \n", "15  U1UBCU00401-003-001  BCU-100 ?-2011 ?-FILTER        GM70583145   \n", "16  U1UBCU00401-003-001  BCU-100 ?-2011 ?-FILTER        GM70583145   \n", "17  U1UBCU00401-003-001  BCU-100 ?-2011 ?-FILTER        GM70583145   \n", "18  U1UBCU00401-003-001  BCU-100 ?-2011 ?-FILTER        GM70583145   \n", "19  U1UBCU00401-003-001  BCU-100 ?-2011 ?-FILTER        GM70583145   \n", "\n", "             stage                             description work_order_type  \\\n", "0   Safety Stage 1                               Blowdowns              PP   \n", "1   Safety Stage 1                               Blowdowns              PP   \n", "2   Safety Stage 1                               Blowdowns              PP   \n", "3   Safety Stage 1                               Blowdowns              PP   \n", "4   Safety Stage 1                               Blowdowns              PP   \n", "5   Safety Stage 1                      Boiler Adjustments              PP   \n", "6   Safety Stage 1                      Boiler Adjustments              PP   \n", "7   Safety Stage 1                      Boiler Adjustments              PP   \n", "8   Safety Stage 1                      Boiler Adjustments              PP   \n", "9   Safety Stage 1                      Boiler Adjustments              PP   \n", "10  Safety Stage 1  Boiler System Maintenance Requirements              PP   \n", "11  Safety Stage 1  Boiler System Maintenance Requirements              PP   \n", "12  Safety Stage 1  Boiler System Maintenance Requirements              PP   \n", "13  Safety Stage 1  Boiler System Maintenance Requirements              PP   \n", "14  Safety Stage 1  Boiler System Maintenance Requirements              PP   \n", "15  Safety Stage 1                    Brix Testing Process              PP   \n", "16  Safety Stage 1                    Brix Testing Process              PP   \n", "17  Safety Stage 1                    Brix Testing Process              PP   \n", "18  Safety Stage 1                    Brix Testing Process              PP   \n", "19  Safety Stage 1                    Brix Testing Process              PP   \n", "\n", "    status  assigned_to                              operation_description  \\\n", "0    DRAFT  <PERSON> Stage 1: Observe Blowdowns (BH-003)   \n", "1    INPRG  <PERSON> Stage 1: Observe Blowdowns (BH-003)   \n", "2   OPCOMP  <PERSON> Stage 1: Observe Blowdowns (BH-003)   \n", "3     COMP  <PERSON> Stage 1: Observe Blowdowns (BH-003)   \n", "4    CLOSE  Derek <PERSON> Stage 1: Observe Blowdowns (BH-003)   \n", "5    DRAFT  <PERSON> Stage 1: Observe Boiler Adjustments (BH...   \n", "6    INPRG  <PERSON> Stage 1: Observe Boiler Adjustments (BH...   \n", "7   OPCOMP  <PERSON> Stage 1: Observe Boiler Adjustments (BH...   \n", "8     COMP  <PERSON> Stage 1: Observe Boiler Adjustments (BH...   \n", "9    CLOSE  Derek <PERSON> Stage 1: Observe Boiler Adjustments (BH...   \n", "10   DRAFT  <PERSON> Stage 1: Observe Boiler System Maintena...   \n", "11   INPRG  <PERSON> Stage 1: Observe Boiler System Maintena...   \n", "12  OPCOMP  <PERSON> Stage 1: Observe Boiler System Maintena...   \n", "13    COMP  <PERSON> Stage 1: Observe Boiler System Maintena...   \n", "14   CLOSE  Derek <PERSON> Stage 1: Observe Boiler System Maintena...   \n", "15   DRAFT  <PERSON> Stage 1: Observe Brix Testing Process (...   \n", "16   INPRG  Derek <PERSON> Stage 1: Observe Brix Testing Process (...   \n", "17  OPCOMP  <PERSON> Stage 1: Observe Brix Testing Process (...   \n", "18    COMP  Derek <PERSON> Stage 1: Observe Brix Testing Process (...   \n", "19   CLOSE  Derek <PERSON> Stage 1: Observe Brix Testing Process (...   \n", "\n", "   operation_result operation_comments      changed_by  \\\n", "0         2830 : OK               TEST  <PERSON>   \n", "1         2832 : OK               TEST  <PERSON>   \n", "2         2834 : OK               TEST  <PERSON>   \n", "3         2836 : OK               TEST  <PERSON>   \n", "4         2838 : OK               TEST  <PERSON>   \n", "5         2850 : OK               TEST   <PERSON>   \n", "6         2852 : OK               TEST   <PERSON>   \n", "7         2854 : OK               TEST   <PERSON>   \n", "8         2856 : OK               TEST   <PERSON>   \n", "9         2858 : OK               TEST   <PERSON>   \n", "10        2870 : OK               TEST        MAXADMIN   \n", "11        2872 : OK               TEST        MAXADMIN   \n", "12        2874 : OK               TEST        MAXADMIN   \n", "13        2876 : OK               TEST        MAXADMIN   \n", "14        2878 : OK               TEST        MAXADMIN   \n", "15        2890 : OK               TEST  <PERSON>   \n", "16        2892 : OK               TEST  <PERSON>   \n", "17        2894 : OK               TEST  <PERSON>   \n", "18        2896 : OK               TEST  <PERSON>   \n", "19        2898 : OK               TEST  <PERSON>   \n", "\n", "                   status_date activity_number  \n", "0   2025-02-06 10:15:20.960000       A00208319  \n", "1   2025-02-07 23:18:48.684000       A00208319  \n", "2   2025-02-09 16:17:00.632000       A00208319  \n", "3   2025-02-10 05:01:37.497000       A00208319  \n", "4   2025-02-11 22:38:55.321000       A00208319  \n", "5   2025-02-23 00:01:06.955000       A00208320  \n", "6   2025-02-25 17:52:04.996000       A00208320  \n", "7   2025-02-27 11:13:14.725000       A00208320  \n", "8   2025-03-03 12:13:25.045000       A00208320  \n", "9   2025-03-05 06:00:49.984000       A00208320  \n", "10  2025-03-23 11:36:25.423000       A00208326  \n", "11  2025-03-27 22:02:18.121000       A00208326  \n", "12  2025-03-29 04:23:08.360000       A00208326  \n", "13  2025-04-02 01:46:18.878000       A00208326  \n", "14  2025-04-04 16:48:36.092000       A00208326  \n", "15  2025-04-17 21:07:10.384000       A00208334  \n", "16  2025-04-19 05:45:20.586000       A00208334  \n", "17  2025-04-21 11:29:18.433000       A00208334  \n", "18  2025-04-23 14:16:02.515000       A00208334  \n", "19  2025-04-26 08:53:18.344000       A00208334  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["def display_table_head(table_name, n=5):\n", "    # Connect to the SQLite database\n", "    conn = sqlite3.connect(DB_PATH)\n", "    \n", "    # Set the number of rows to display\n", "    n = 20\n", "    \n", "    try:\n", "        # Import pandas if not already imported\n", "        import pandas as pd\n", "        \n", "        # Read the first n rows from the table\n", "        query = f\"SELECT * FROM {table_name} LIMIT {n}\"\n", "        df = pd.read_sql_query(query, conn)\n", "        \n", "        # Display the dataframe\n", "        print(f\"\\nFirst {n} rows of {table_name} table:\")\n", "        return df.head(n)\n", "        \n", "    except Exception as e:\n", "        print(f\"Error: {e}\")\n", "        return None\n", "    finally:\n", "        # Close the connection\n", "        conn.close()\n", "\n", "# Display the head of the selected table\n", "display_table_head(table_choice)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.6"}}, "nbformat": 4, "nbformat_minor": 2}