<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authorization</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='sidebar.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@24,400,0,0">
</head>
<body>
    {% include 'sidebar.html' %}
    <div class="container">
        <h1>Authorization</h1>

        <div class="table-container">
            <h2>Grant Access</h2>
            <table id="addAccessTable">
                <thead>
                    <tr>
                        <th>Department</th>
                        <th>Employee Login ID</th>
                        <th>Access</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <select id="department" required>
                                <option value="">Select a department...</option>
                                <option value="Facility Maintenance">Facility Maintenance</option>
                                <option value="Not Facility Maintenance">Not Facility Maintenance</option>
                            </select>
                        </td>
                        <td>
                            <input type="text" id="employeeLogin" required>
                        </td>
                        <td>
                            <select id="access" required>
                                <option value="">Select access level...</option>
                                <option value="Admin">Admin - Modify Organization Chart and Grant Authorization with in department</option>
                                <option value="User">User - View and Download Data</option>
                                <option value="View">View - View Only</option>
                            </select>
                        </td>
                        <td>
                            <button id="addAccessBtn" class="btn-primary">Add</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="table-container">
            <h2>Employee Access List</h2>
            <table id="employeeAccessTable">
                <thead>
                    <tr>
                        <th>Department</th>
                        <th>Employee Login</th>
                        <th>Access</th>
                        <th>Remove</th>
                    </tr>
                    <tr class="filter-row">
                        <th><input type="text" class="table-filter" id="departmentFilter" placeholder="Filter Department..."></th>
                        <th><input type="text" class="table-filter" id="employeeLoginFilter" placeholder="Filter ID..."></th>
                        <th><input type="text" class="table-filter" id="accessFilter" placeholder="Filter Access..."></th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    {% if authorizations %}
                        {% for auth in authorizations %}
                        <tr data-id="{{ auth.id }}">
                            <td>{{ auth.department }}</td>
                            <td>{{ auth.employee_login }}</td>
                            <td>{{ auth.access_level }}</td>
                            <td>
                                <button class="btn-delete" onclick="deleteAuthorization('{{ auth.id }}')">Delete</button>
                            </td>
                        </tr>
                        {% endfor %}
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>

    <script src="{{ url_for('static', filename='sidebar.js') }}"></script>
    <script src="{{ url_for('static', filename='authorization.js') }}"></script>
</body>
</html>



