import sqlite3
import os
from datetime import datetime

# Database path - make sure this points to your Data.db location
DB_PATH = os.path.join('data', 'Data.db')

def get_db_connection():
    """Create a database connection and return the connection and cursor"""
    # Ensure the data directory exists
    os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)
    
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row  # This enables column access by name
    return conn

def init_db():
    """Initialize the database and create the employeelist table if it doesn't exist"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Create the employeelist table if it doesn't exist
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employeelist (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                department TEXT NOT NULL,
                name TEXT NOT NULL,
                trade TEXT NOT NULL,
                crew TEXT NOT NULL,
                start_date DATE NOT NULL
            )
        ''')
        
        conn.commit()
        print(f"Database initialized successfully at {os.path.abspath(DB_PATH)}")
    except sqlite3.Error as e:
        print(f"Database initialization error: {e}")
    finally:
        conn.close()

def add_employee(department, name, trade, crew, start_date):
    """Add a new employee to the database"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Check if employee already exists with the same name, trade, and crew
        cursor.execute('''
            SELECT id FROM employeelist 
            WHERE name = ? AND trade = ? AND crew = ?
        ''', (name, trade, crew))
        
        if cursor.fetchone() is not None:
            raise ValueError("Employee already exists with the same name, trade, and crew")
        
        # Insert the new employee
        cursor.execute('''
            INSERT INTO employeelist (department, name, trade, crew, start_date)
            VALUES (?, ?, ?, ?, ?)
        ''', (department, name, trade, crew, start_date))
        
        employee_id = cursor.lastrowid
        conn.commit()
        
        # Return the new employee data including the ID
        return {
            'id': employee_id,
            'department': department,
            'name': name,
            'trade': trade,
            'crew': crew,
            'start_date': start_date
        }
    except sqlite3.Error as e:
        raise Exception(f"Database error: {e}")
    finally:
        conn.close()

def get_all_employees():
    """Retrieve all employees from the database"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM employeelist')
        employees = [dict(row) for row in cursor.fetchall()]
        return employees
    except sqlite3.Error as e:
        raise Exception(f"Database error: {e}")
    finally:
        conn.close()

def delete_employee(employee_id):
    """Delete an employee from the database"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute('DELETE FROM employeelist WHERE id = ?', (employee_id,))
        conn.commit()
        return cursor.rowcount > 0
    except sqlite3.Error as e:
        raise Exception(f"Database error: {e}")
    finally:
        conn.close()

# Initialize the database when the module is loaded
if __name__ == "__main__":
    init_db()

