# import requests
# import json
# import urllib3

# # Disable HTTPS warnings
# urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# # --- Configuration ---
# BASE_URL = "https://ems-lgensol.singlex.com/maximo"
# OBJECT_STRUCTURE = "MXWO"
# API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"
# WORKORDER_NUM = "GM15223259"
# OUTPUT_FILE = "single_workorder.json"

# # --- Request setup ---
# endpoint = f"{BASE_URL}/oslc/os/{OBJECT_STRUCTURE}"
# params = {
#     "oslc.where": f'wonum="{WORKORDER_NUM}"',
#     "oslc.select": "wonum,description,ownergroup,changedate,workorderid",
#     "lean": "1"
# }
# headers = {
#     "Accept": "application/json",
#     "apikey": API_KEY
# }

# # --- API Request ---
# print(f"🔍 Getting work order {WORKORDER_NUM}...")
# response = requests.get(endpoint, headers=headers, params=params, verify=False)

# if response.status_code == 200:
#     data = response.json()
#     wo = data.get("member", [])
    
#     if wo:
#         print("✅ Work order found.")
#         with open(OUTPUT_FILE, "w") as f:
#             json.dump(wo[0], f, indent=2)
#         print(f"📄 Saved to {OUTPUT_FILE}")
#     else:
#         print("⚠️ Work order not found.")
# else:
#     print(f"❌ Request failed: {response.status_code}")
#     print(response.text)


import requests
import urllib3
import pandas as pd

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

BASE_URL = "https://ems-lgensol.singlex.com/maximo"
OBJECT_STRUCTURE = "MXWO"
API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"
OUTPUT_FILE = "filtered_workorders.json"

# ✅ Avoid parentheses — use IN instead
where_clause = (
    'status="DRAFT" and '
    'woclass in ["WORKORDER","ACTIVITY"] and '
    'historyflag=0 and '
    'istask=0 and '
    'siteid="UTIL.GM" and '
    'ownergroup!="GM.UT.U"'
)

params = {
    "oslc.where": where_clause,
    "oslc.select": "wonum,description,ownergroup,schedstart,schedfinish,jpnum,assentnum",
    "lean": "1"
}

headers = {
    "Accept": "application/json",
    "apikey": API_KEY
}

print("🔍 Querying work orders with OSLC-safe filter...")
response = requests.get(f"{BASE_URL}/oslc/os/{OBJECT_STRUCTURE}", headers=headers, params=params, verify=False)

if response.status_code == 200:
    data = response.json()
    members = data.get("member", [])
    print(f"✅ Retrieved {len(members)} records.")

    # Convert to DataFrame
    df = pd.json_normalize(members)

    # Optional: Drop technical columns like '_rowstamp' and 'href'
    df = df.drop(columns=["_rowstamp", "href"], errors="ignore")

    # Export to Excel
    df.to_excel(OUTPUT_FILE, index=False)
    print(f"📄 Saved to {OUTPUT_FILE}")

else:
    print(f"❌ Request failed: {response.status_code}")
    print(response.text)
