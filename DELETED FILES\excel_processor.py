import os
import sqlite3
import pandas as pd
import sys

def process_excel_file():
    """
    Process the Excel file:
    1. Connect to the SQLite database and extract the WorkOrder table
    2. Create a new Excel file with a sheet called 'AppData' containing the WorkOrder data
    3. Save the file as 'Facility Flex Chart LOCAL.xlsx'
    """
    try:
        # Print current working directory for debugging
        print(f"Current working directory: {os.getcwd()}")

        # Define file paths
        output_path = os.path.join('data', 'Facility Flex Chart LOCAL.xlsx')
        db_path = os.path.join('data', 'Data.db')

        print(f"Output path: {os.path.abspath(output_path)}")
        print(f"Database path: {os.path.abspath(db_path)}")

        # Ensure the data directory exists
        os.makedirs('data', exist_ok=True)
        print(f"Data directory created/verified")

        # Check if database file exists
        if not os.path.exists(db_path):
            print(f"Error: Database file not found at {db_path}")
            return False

        # Connect to the SQLite database
        print(f"Connecting to database: {db_path}")
        conn = sqlite3.connect(db_path)

        # Check if WorkOrder table exists
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='WorkOrder'")
        if not cursor.fetchone():
            print("Error: WorkOrder table does not exist in the database")
            conn.close()
            return False

        # Query the WorkOrder table
        query = "SELECT * FROM WorkOrder"
        print("Extracting WorkOrder data from database...")
        df = pd.read_sql_query(query, conn)

        # Print the number of rows retrieved
        print(f"Retrieved {len(df)} rows from WorkOrder table")

        # Close the database connection
        conn.close()

        # Create a new Excel file with the WorkOrder data
        print(f"Creating new Excel file: {output_path}")

        # Write the WorkOrder data to the Excel file
        print("Writing 'AppData' sheet with WorkOrder data")
        df.to_excel(output_path, sheet_name='AppData', index=False)

        print(f"Excel file saved successfully to: {output_path}")

        print("Process completed successfully!")
        return True

    except Exception as e:
        print(f"Error: {str(e)}")
        return False

if __name__ == "__main__":
    success = process_excel_file()
    if not success:
        print("Script execution failed")
        sys.exit(1)
    else:
        print("Script execution completed successfully")
        sys.exit(0)
