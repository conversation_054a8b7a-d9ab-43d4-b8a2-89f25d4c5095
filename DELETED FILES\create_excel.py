import os
import sqlite3
import pandas as pd

def create_excel():
    """Create a new Excel file with the WorkOrder data"""
    try:
        # Define file paths
        output_path = os.path.join('data', 'Facility Flex Chart LOCAL.xlsx')
        db_path = os.path.join('data', 'Data.db')
        
        print(f"Output path: {output_path}")
        print(f"Database path: {db_path}")
        
        # Ensure the data directory exists
        os.makedirs('data', exist_ok=True)
        
        # Connect to the database
        conn = sqlite3.connect(db_path)
        
        # Get the WorkOrder data
        df = pd.read_sql_query("SELECT * FROM WorkOrder", conn)
        
        # Close the connection
        conn.close()
        
        # Create the Excel file
        df.to_excel(output_path, sheet_name='AppData', index=False)
        
        print(f"Excel file created successfully: {output_path}")
        return True
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

if __name__ == "__main__":
    create_excel()
