import requests
import json
from urllib.parse import quote

# Configuration
BASE_URL = "https://ems-lgensol.singlex.com"
WORKORDER_NUM = "GM15086041"
API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"
NEW_DESCRIPTION = "U1UAHU01001-003-001 - AHU-400-1017 Bag Filter Replacement - TEST PUT"

def get_workorder_details():
    """
    Get current work order details using maxrest API with API key
    """
    # Construct URL for getting work order details
    url = f"{BASE_URL}/maxrest/rest/mbo/workorder/{WORKORDER_NUM}"
    
    # Try different API key header formats
    headers_variants = [
        {
            'Accept': 'application/json',
            'apikey': API_KEY
        },
        {
            'Accept': 'application/json',
            'Authorization': f'Bearer {API_KEY}'
        },
        {
            'Accept': 'application/json',
            'X-API-Key': API_KEY
        },
        {
            'Accept': 'application/json',
            'MAXAUTH': API_KEY
        }
    ]
    
    params = {
        '_compact': 'true',
        '_format': 'json'
    }
    
    print(f"Retrieving work order details for: {WORKORDER_NUM}")
    print(f"URL: {url}")
    
    for i, headers in enumerate(headers_variants):
        print(f"\nTrying authentication method {i+1}: {list(headers.keys())}")
        
        try:
            response = requests.get(url, headers=headers, params=params, timeout=30)
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    workorder_data = response.json()
                    print("✅ Work order details retrieved successfully!")
                    
                    # Extract relevant information
                    if isinstance(workorder_data, dict):
                        print(f"Work Order Number: {workorder_data.get('wonum', 'N/A')}")
                        print(f"Current Description: {workorder_data.get('description', 'N/A')}")
                        print(f"Status: {workorder_data.get('status', 'N/A')}")
                        print(f"Site ID: {workorder_data.get('siteid', 'N/A')}")
                        print("-" * 60)
                        return workorder_data, headers
                    else:
                        print(f"Unexpected response format: {type(workorder_data)}")
                        
                except json.JSONDecodeError as e:
                    print(f"JSON decode error: {e}")
                    print(f"Response content: {response.text[:300]}...")
            else:
                print(f"Response preview: {response.text[:200]}...")
                
        except Exception as e:
            print(f"Error with method {i+1}: {str(e)}")
    
    print("❌ Could not retrieve work order details with any authentication method")
    return None, None

def update_workorder_title_method1(auth_headers):
    """
    Method 1: Update using query parameters with API key
    """
    print("\n=== Method 1: Using Query Parameters ===")
    
    # Construct URL with all parameters
    url = f"{BASE_URL}/maxrest/rest/mbo/workorder/{WORKORDER_NUM}"
    
    params = {
        '_compact': 'true',
        '_format': 'json',
        '~description': NEW_DESCRIPTION,
        '~wo': 'this'
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        **auth_headers  # Include the working authentication headers
    }
    
    print(f"URL: {url}")
    print(f"Parameters: {params}")
    
    try:
        response = requests.put(url, params=params, headers=headers, timeout=30)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code in [200, 201, 204]:
            print("✅ Work order updated successfully using Method 1!")
            
            if response.text:
                try:
                    response_data = response.json()
                    updated_desc = response_data.get('description', 'N/A')
                    print(f"Updated description: {updated_desc}")
                except json.JSONDecodeError:
                    print(f"Response: {response.text[:200]}...")
            
            return True
        else:
            print(f"Method 1 failed with status {response.status_code}")
            print(f"Response: {response.text[:300]}...")
            
    except Exception as e:
        print(f"Method 1 error: {str(e)}")
    
    return False

def update_workorder_title_method2(auth_headers):
    """
    Method 2: Update using JSON payload in request body with API key
    """
    print("\n=== Method 2: Using JSON Payload ===")
    
    url = f"{BASE_URL}/maxrest/rest/mbo/workorder/{WORKORDER_NUM}"
    
    params = {
        '_compact': 'true',
        '_format': 'json'
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        **auth_headers  # Include the working authentication headers
    }
    
    # JSON payload with the new description
    payload = {
        'description': NEW_DESCRIPTION
    }
    
    print(f"URL: {url}")
    print(f"Payload: {payload}")
    
    try:
        response = requests.put(
            url, 
            params=params, 
            headers=headers, 
            data=json.dumps(payload),
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code in [200, 201, 204]:
            print("✅ Work order updated successfully using Method 2!")
            
            if response.text:
                try:
                    response_data = response.json()
                    updated_desc = response_data.get('description', 'N/A')
                    print(f"Updated description: {updated_desc}")
                except json.JSONDecodeError:
                    print(f"Response: {response.text[:200]}...")
            
            return True
        else:
            print(f"Method 2 failed with status {response.status_code}")
            print(f"Response: {response.text[:300]}...")
            
    except Exception as e:
        print(f"Method 2 error: {str(e)}")
    
    return False

def update_workorder_title_method3(auth_headers):
    """
    Method 3: POST with method override and API key
    """
    print("\n=== Method 3: POST with Method Override ===")
    
    url = f"{BASE_URL}/maxrest/rest/mbo/workorder/{WORKORDER_NUM}"
    
    params = {
        '_compact': 'true',
        '_format': 'json'
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'x-http-method-override': 'PUT',
        **auth_headers  # Include the working authentication headers
    }
    
    payload = {
        'description': NEW_DESCRIPTION
    }
    
    print(f"URL: {url}")
    print(f"Payload: {payload}")
    
    try:
        response = requests.post(
            url, 
            params=params, 
            headers=headers, 
            data=json.dumps(payload),
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code in [200, 201, 204]:
            print("✅ Work order updated successfully using Method 3!")
            
            if response.text:
                try:
                    response_data = response.json()
                    updated_desc = response_data.get('description', 'N/A')
                    print(f"Updated description: {updated_desc}")
                except json.JSONDecodeError:
                    print(f"Response: {response.text[:200]}...")
            
            return True
        else:
            print(f"Method 3 failed with status {response.status_code}")
            print(f"Response: {response.text[:300]}...")
            
    except Exception as e:
        print(f"Method 3 error: {str(e)}")
    
    return False

def main():
    print("=== Maximo Work Order Update Script (maxrest API with API Key) ===")
    print(f"Target Work Order: {WORKORDER_NUM}")
    print(f"Base URL: {BASE_URL}")
    print(f"API Key: {API_KEY[:8]}...{API_KEY[-8:]}")  # Show partial key for verification
    print(f"New Description: {NEW_DESCRIPTION}")
    print("=" * 60)
    
    # First, try to get current work order details
    print("Step 1: Retrieving current work order details...")
    current_details, working_auth_headers = get_workorder_details()
    
    if not current_details or not working_auth_headers:
        print("⚠️  Could not retrieve work order details. Check API key and work order number.")
        print("Cannot proceed with update without successful authentication.")
        return
    
    print(f"\nStep 2: Attempting to update work order description...")
    print(f"Using authentication method: {list(working_auth_headers.keys())}")
    
    # Try different update methods using the working authentication headers
    methods = [
        lambda: update_workorder_title_method1(working_auth_headers),
        lambda: update_workorder_title_method2(working_auth_headers), 
        lambda: update_workorder_title_method3(working_auth_headers)
    ]
    
    success = False
    for i, method in enumerate(methods, 1):
        if method():
            success = True
            break
    
    if success:
        print("\n🎉 Work order update completed successfully!")
        
        # Verify the update by getting details again
        print("\nStep 3: Verifying the update...")
        updated_details, _ = get_workorder_details()
        
    else:
        print("\n❌ All update methods failed")
        print("\n💡 Troubleshooting checklist:")
        print("1. ✓ API key is valid and has sufficient permissions")
        print("2. ✓ Work order number exists and is accessible")
        print("3. ✓ Work order is not locked or in CLOSE status")
        print("4. ✓ maxrest API is enabled on your Maximo instance")
        print("5. ✓ User associated with API key can modify work orders")
        print("6. ✓ Network connectivity to Maximo server")
    
    print("\nScript completed.")

if __name__ == "__main__":
    main()