import os
import sqlite3
import pandas as pd

# Define paths
data_dir = 'data'
db_path = os.path.join(data_dir, 'Data.db')
output_path = os.path.join(data_dir, 'Facility Flex Chart LOCAL.xlsx')

# Create data directory if it doesn't exist
os.makedirs(data_dir, exist_ok=True)

# Connect to the database
print(f"Connecting to database: {db_path}")
conn = sqlite3.connect(db_path)

# Get the data from the WorkOrder table
print("Retrieving data from WorkOrder table...")
df = pd.read_sql_query("SELECT * FROM WorkOrder", conn)
print(f"Retrieved {len(df)} rows from WorkOrder table")

# Close the database connection
conn.close()

# Create the Excel file
print(f"Creating Excel file: {output_path}")
df.to_excel(output_path, sheet_name='AppData', index=False)

print(f"Excel file created successfully: {output_path}")
