import os
import sqlite3
import pandas as pd
import shutil

def main():
    # Define paths
    data_dir = 'data'
    template_path = os.path.join(data_dir, 'Facility Flex Chart template.xlsx')
    output_path = os.path.join(data_dir, 'Facility Flex Chart LOCAL.xlsx')
    db_path = os.path.join(data_dir, 'Data.db')
    
    # Create data directory if it doesn't exist
    os.makedirs(data_dir, exist_ok=True)
    
    # Connect to the database
    conn = sqlite3.connect(db_path)
    
    # Get the data from the WorkOrder table
    df = pd.read_sql_query("SELECT * FROM WorkOrder", conn)
    
    # Close the database connection
    conn.close()
    
    # First, make a copy of the template file
    shutil.copy2(template_path, output_path)
    
    # Now add the WorkOrder data as a new sheet
    with pd.ExcelWriter(output_path, engine='openpyxl', mode='a') as writer:
        df.to_excel(writer, sheet_name='AppData', index=False)
    
    print(f"Excel file created successfully: {output_path}")

if __name__ == "__main__":
    main()
