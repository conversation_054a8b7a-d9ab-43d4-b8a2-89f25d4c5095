// Helper functions for loading indicator
function showLoadingIndicator() {
    const loadingDiv = document.createElement('div');
    loadingDiv.id = 'gantt-loading';
    loadingDiv.className = 'gantt-loading';
    loadingDiv.innerHTML = `
        <div class="loading-spinner"></div>
        <div class="loading-text">Loading data: <span id="loading-progress">0%</span></div>
    `;

    // Add some basic styles
    const style = document.createElement('style');
    style.textContent = `
        .gantt-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
            z-index: 100;
            text-align: center;
        }
        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 2s linear infinite;
            margin: 0 auto 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    `;
    document.head.appendChild(style);

    document.getElementById('gantt-container').appendChild(loadingDiv);
}

function updateLoadingProgress(percent) {
    const progressElement = document.getElementById('loading-progress');
    if (progressElement) {
        progressElement.textContent = `${Math.round(percent)}%`;
    }
}

function hideLoadingIndicator() {
    const loadingDiv = document.getElementById('gantt-loading');
    if (loadingDiv) {
        loadingDiv.remove();
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Configure gantt
    gantt.config.xml_date = "%Y-%m-%d %H:%i";
    gantt.config.date_format = "%Y-%m-%d %H:%i";

    // Disable features that aren't needed for better performance
    gantt.config.sort = false; // Disable automatic sorting
    gantt.config.drag_progress = false; // Disable progress dragging if not needed
    gantt.config.drag_resize = false; // Disable task resizing if not needed
    gantt.config.drag_links = false; // Disable link dragging if not needed
    gantt.config.details_on_dblclick = false; // Disable default behavior
    gantt.attachEvent("onTaskDblClick", function(id, e) {
        // Get the task data
        const task = gantt.getTask(id);

        // Create a modal dialog for displaying task details
        const modalDiv = document.createElement('div');
        modalDiv.className = 'gantt-task-details-modal';

        // Get task level to determine what details to show
        const level = getTaskLevel(task);
        let levelName = "";

        // Check if this task has multiple work orders
        const hasMultipleWorkOrders = task.has_multiple_work_orders || false;

        switch(level) {
            case 0: levelName = "Technician"; break;
            case 1: levelName = "Stage"; break;
            case 2: levelName = "Work Order"; break;
            case 3:
                // For descriptions with multiple work orders, level 3 is work_order_number and level 4 is status
                if (task.has_multiple_work_orders || getParentTask(task).has_multiple_work_orders) {
                    levelName = "Work Order Number";
                } else {
                    levelName = "Status";
                }
                break;
            case 4: levelName = "Status"; break; // Only for descriptions with multiple work orders
            default: levelName = "Task";
        }

        // Helper function to get parent task
        function getParentTask(task) {
            if (!task.parent || task.parent === 0) {
                return null;
            }
            return gantt.getTask(task.parent);
        }

        // Determine what fields to display based on task level
        let detailsHTML = `
            <div class="gantt-task-details-header">
                <h3>${levelName} Details</h3>
                <button class="close-btn">&times;</button>
            </div>
            <div class="gantt-task-details-content">
                <table class="details-table">
        `;

        // Add level-specific details
        if (level === 0) { // Technician level
            detailsHTML += `
                    <tr>
                        <th>Name:</th>
                        <td>${task.text || 'N/A'}</td>
                    </tr>
                    <tr>
                        <th>Crew:</th>
                        <td>${task.crew || 'N/A'}</td>
                    </tr>
                    <tr>
                        <th>Progress:</th>
                        <td>${Math.round(task.progress * 100)}%</td>
                    </tr>
                    <tr>
                        <th>Start Date:</th>
                        <td>${gantt.templates.task_date(task.start_date) || 'N/A'}</td>
                    </tr>
                    <tr>
                        <th>End Date:</th>
                        <td>${gantt.templates.task_date(task.end_date) || 'N/A'}</td>
                    </tr>
            `;
        } else if (level === 1) { // Stage level
            detailsHTML += `
                    <tr>
                        <th>Name:</th>
                        <td>${task.text || 'N/A'}</td>
                    </tr>
                    <tr>
                        <th>Parent:</th>
                        <td>${gantt.getTask(task.parent).text || 'N/A'}</td>
                    </tr>
                    <tr>
                        <th>Progress:</th>
                        <td>${Math.round(task.progress * 100)}%</td>
                    </tr>
                    <tr>
                        <th>Start Date:</th>
                        <td>${gantt.templates.task_date(task.start_date) || 'N/A'}</td>
                    </tr>
                    <tr>
                        <th>End Date:</th>
                        <td>${gantt.templates.task_date(task.end_date) || 'N/A'}</td>
                    </tr>
            `;
        } else if (level === 2) { // Work Order level
            // Get the work order number directly from the task
            const workOrderNumber = task.work_order_number || 'N/A';

            detailsHTML += `
                    <tr>
                        <th>Name:</th>
                        <td>${task.text || 'N/A'}</td>
                    </tr>
                    <tr>
                        <th>Work Order Number:</th>
                        <td>${workOrderNumber}</td>
                    </tr>
                    <!-- COMMENT OUT HTML
                    <tr>
                        <th>Description:</th>
                        <td>${task.text || 'N/A'}</td>
                    </tr>
                    COMMENT OUT HTML-->
                    <tr>
                        <th>Progress:</th>
                        <td>${Math.round(task.progress * 100)}%</td>
                    </tr>
                    <tr>
                        <th>Start Date:</th>
                        <td>${gantt.templates.task_date(task.start_date) || 'N/A'}</td>
                    </tr>
                    <tr>
                        <th>End Date:</th>
                        <td>${gantt.templates.task_date(task.end_date) || 'N/A'}</td>
                    </tr>
            `;
        } else if (level === 3 && (task.has_multiple_work_orders || getParentTask(task).has_multiple_work_orders)) { // Work Order Number level for descriptions with multiple work orders
            detailsHTML += `
                    <tr>
                        <th>Work Order Number:</th>
                        <td>${task.text || 'N/A'}</td>
                    </tr>
                    <tr>
                        <th>Parent Description:</th>
                        <td>${gantt.getTask(task.parent).text || 'N/A'}</td>
                    </tr>
                    <tr>
                        <th>Progress:</th>
                        <td>${Math.round(task.progress * 100)}%</td>
                    </tr>
            `;
        } else if ((level === 3 && !getParentTask(task).has_multiple_work_orders) || level === 4) { // Status level
            detailsHTML += `
                    <tr>
                        <th>Status:</th>
                        <td>${task.status || 'N/A'}</td>
                    </tr>
                    <tr>
                        <th>Work Order:</th>
                        <td>${task.work_order_number || 'N/A'}</td>
                    </tr>
                    <tr>
                        <th>Start Date:</th>
                        <td>${gantt.templates.task_date(task.start_date)}</td>
                    </tr>
                    <tr>
                        <th>End Date:</th>
                        <td>${gantt.templates.task_date(task.end_date)}</td>
                    </tr>
            `;
        }

        // Close the table and content div
        detailsHTML += `
                </table>
            </div>
        `;

        // Set the modal content
        modalDiv.innerHTML = detailsHTML;

        // Add styles for the modal
        const style = document.createElement('style');
        style.textContent = `
            .gantt-task-details-modal {
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                padding: 0;
                border-radius: 5px;
                box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
                z-index: 1000;
                width: 400px;
                max-width: 90%;
                max-height: 90vh;
                overflow-y: auto;
            }
            .gantt-task-details-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 10px 15px;
                background: #f5f5f5;
                border-bottom: 1px solid #ddd;
                border-radius: 5px 5px 0 0;
            }
            .gantt-task-details-header h3 {
                margin: 0;
                font-size: 18px;
            }
            .close-btn {
                background: none;
                border: none;
                font-size: 24px;
                cursor: pointer;
                color: #666;
            }
            .close-btn:hover {
                color: #333;
            }
            .gantt-task-details-content {
                padding: 15px;
            }
            .details-table {
                width: 100%;
                border-collapse: collapse;
            }
            .details-table th, .details-table td {
                padding: 8px;
                text-align: left;
                border-bottom: 1px solid #eee;
            }
            .details-table th {
                width: 40%;
                font-weight: bold;
                color: #555;
            }
        `;
        document.head.appendChild(style);

        // Add the modal to the document
        document.body.appendChild(modalDiv);

        // Add event listener for the close button
        const closeBtn = modalDiv.querySelector('.close-btn');
        closeBtn.addEventListener('click', function() {
            modalDiv.remove();
        });

        // Also close when clicking outside the modal
        document.addEventListener('click', function closeModal(e) {
            if (!modalDiv.contains(e.target) && e.target !== modalDiv) {
                modalDiv.remove();
                document.removeEventListener('click', closeModal);
            }
        });

        return true; // Prevent default behavior
    });
    // gantt.config.autosize = "y"; // Only autosize vertically
    gantt.config.row_height = 30;
    gantt.config.task_height = 12;

    // Use dynamic loading for task rendering
    gantt.config.show_task_cells = false; // Hide task cells when not needed
    gantt.config.smart_rendering = true; // Enable smart rendering
    gantt.config.smart_scales = true; // Enable smart scales

    // Initial scale configuration (day scale by default)
    gantt.config.scale_unit = "month";
    gantt.config.date_scale = "%F"; // Month on top row
    gantt.config.min_column_width = 40;
    gantt.config.duration_unit = "day";
    gantt.config.row_height = 30;
    gantt.config.fit_tasks = true;

    // Set up subscales for two-row display
    gantt.config.subscales = [
        {unit: "day", step: 1, date: "%d"} // Date on bottom row
    ];

    // Enable tree grid
    gantt.config.open_tree_initially = true;

    // Configure columns
    gantt.config.columns = [
        {name: "text", label: "OJT List", tree: true, width: 350, template: function(task) {
            // Just return the text without any color coding
            return task.text;
        }},
        {name: "progress", label: "Complete %", width: 110, align: "center", template: function(task) {
            // Get the task level
            const level = getTaskLevel(task);

            // Don't display percentage for Status level (level 3 or 4 depending on hierarchy)
            if ((level === 3 && !task.has_multiple_work_orders && !getParentTask(task)?.has_multiple_work_orders) ||
                (level === 4)) {
                // Even for status level, we need to get the crew color for the cell background
                let currentTask = task;

                // Traverse up the tree to find the level 0 parent
                while (getTaskLevel(currentTask) > 0) {
                    const parentId = currentTask.parent;
                    if (parentId) {
                        currentTask = gantt.getTask(parentId);
                    } else {
                        break;
                    }
                }

                // Now currentTask should be the level 0 parent
                const crew = currentTask.crew || "";
                let crewClass = "";

                switch(crew) {
                    case "A": crewClass = "crew-A"; break;
                    case "B": crewClass = "crew-B"; break;
                    case "C": crewClass = "crew-C"; break;
                    case "D": crewClass = "crew-D"; break;
                    default: crewClass = "";
                }

                // Return an empty div with just the crew background color
                if (crewClass) {
                    return `<div class="crew-cell ${crewClass}"></div>`;
                }

                return "";
            }

            // Get the top-level parent (level 0) to determine crew color
            let currentTask = task;

            // Traverse up the tree to find the level 0 parent
            while (getTaskLevel(currentTask) > 0) {
                const parentId = currentTask.parent;
                if (parentId) {
                    currentTask = gantt.getTask(parentId);
                } else {
                    break;
                }
            }

            // Now currentTask should be the level 0 parent
            const crew = currentTask.crew || "";
            let crewClass = "";

            switch(crew) {
                case "A": crewClass = "crew-A"; break;
                case "B": crewClass = "crew-B"; break;
                case "C": crewClass = "crew-C"; break;
                case "D": crewClass = "crew-D"; break;
                default: crewClass = "";
            }

            // For Name level (level 0), add status icon based on stage number
            if (level === 0) {
                // Get all child tasks (stages)
                const childTasks = gantt.getChildren(task.id);
                let maxStageNumber = 0;

                // Find the highest stage number
                childTasks.forEach(childId => {
                    const childTask = gantt.getTask(childId);
                    const stageName = childTask.text || "";

                    // Extract stage number from text like "Boiler Stage 1"
                    const stageMatch = stageName.match(/Stage\s+(\d+)/i);
                    if (stageMatch && stageMatch[1]) {
                        const stageNumber = parseInt(stageMatch[1]);
                        if (stageNumber > maxStageNumber) {
                            maxStageNumber = stageNumber;
                        }
                    }
                });

                // Determine icon based on max stage number
                let stageIcon = "";
                switch(maxStageNumber) {
                    case 1: stageIcon = " <span style='font-size: 1.5em;'>◔</span>"; break; // Quarter full
                    case 2: stageIcon = " <span style='font-size: 1.5em;'>◑</span>"; break; // Half full
                    case 3: stageIcon = " <span style='font-size: 1.5em;'>◕</span>"; break; // Three quarters full
                    case 4: stageIcon = " <span style='font-size: 1.3em;'>⬤</span>"; break; // Full circle
                    default: stageIcon = ""; // No icon
                }

                // Always apply crew background color if available
                if (crewClass) {
                    return `<div class="crew-cell ${crewClass}">${Math.round(task.progress * 100)}%${stageIcon}</div>`;
                }

                // Return percentage with stage icon (no color)
                return Math.round(task.progress * 100) + "%" + stageIcon;
            }

            // For all other levels, always apply the crew color
            if (crewClass) {
                return `<div class="crew-cell ${crewClass}">${Math.round(task.progress * 100)}%</div>`;
            }

            // Default: just return the percentage
            return Math.round(task.progress * 100) + "%";
        }}
    ];

    // Add scale selector toolbar
    const scaleSelector = document.createElement('div');
    scaleSelector.className = 'scale-selector';
    scaleSelector.innerHTML = `
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
            <div style="display: flex;">
                <div class="department-filter" style="display: flex; align-items: center;">
                    <span style="margin-right: 10px;">Trade:</span>
                    <select id="department-filter" style="width: auto;">
                        <!-- Will be populated dynamically -->
                    </select>
                </div>
                <div class="hierarchy-controls" style="margin-left: 20px; border-left: 1px solid #ccc; padding-left: 20px;">
                    <span style="margin-right: 10px;">Expand to:</span>
                    <button id="expand-name">Name</button>
                    <button id="expand-stage">Stage</button>
                    <button id="expand-description">Description</button>
                    <button id="expand-work-order" style="display: none;">Work Order</button>
                    <button id="expand-status">Status</button>
                </div>
                <div class="crew-legend" style="margin-left: 20px; border-left: 1px solid #ccc; padding-left: 20px; display: flex; align-items: center;">
                    <span style="margin-right: 10px;">Crew:</span>
                    <div style="display: flex; align-items: center;">
                        <div style="background-color: #ffcdd2; width: 20px; height: 20px; margin-right: 5px; border-radius: 3px;"></div>
                        <span style="margin-right: 10px;">A</span>
                        <div style="background-color: #c8e6c9; width: 20px; height: 20px; margin-right: 5px; border-radius: 3px;"></div>
                        <span style="margin-right: 10px;">B</span>
                        <div style="background-color: #bbdefb; width: 20px; height: 20px; margin-right: 5px; border-radius: 3px;"></div>
                        <span style="margin-right: 10px;">C</span>
                        <div style="background-color: #fff9c4; width: 20px; height: 20px; margin-right: 5px; border-radius: 3px;"></div>
                        <span>D</span>
                    </div>
                </div>
            </div>
            <div class="time-scale-controls">
                <button id="scale-year">Year</button>
                <button id="scale-month">Month</button>
                <button id="scale-week">Week</button>
                <button id="scale-day" class="active">Day</button>
                <button id="scale-hour">Hour</button>
            </div>
        </div>
    `;
    document.getElementById('gantt-container').parentNode.insertBefore(scaleSelector, document.getElementById('gantt-container'));

    // Add CSS for the buttons, hierarchy level colors, and crew colors
    const style = document.createElement('style');
    style.textContent = `
        .scale-selector button {
            padding: 6px 12px;
            margin-right: 5px;
            background-color: #f0f0f0;
            border: 1px solid #ccc;
            border-radius: 4px;
            cursor: pointer;
        }
        .scale-selector button.active {
            background-color: #007bff;
            color: white;
            border-color: #0056b3;
        }
        .hierarchy-controls button {
            background-color: #e9ecef;
        }

        /* Hierarchy level colors - different shades of blue */
        .gantt_task_line.level_0 {
            background-color: #001a42; /* Darkest blue for Name level */
            border-color: #0a3880;
        }
        .gantt_task_line.level_1 {
            background-color: #0263fa; /* Dark blue for Stage level */
            border-color: #0263fa;
        }
        .gantt_task_line.level_2 {
            background-color: #a6c6f7; /* Medium blue for Description level */
            border-color: #64b5f6;
        }

        /* Crew cell styling */
        .crew-cell {
            padding: 5px;
            border-radius: 3px;
            width: 100%;
            height: 100%;
            display: block;
            min-height: 20px; /* Ensure empty cells still have height */
        }
        .crew-A {
            background-color: #ffcdd2 !important; /* Light red for Crew A */
        }
        .crew-B {
            background-color: #c8e6c9 !important; /* Light green for Crew B */
        }
        .crew-C {
            background-color: #bbdefb !important; /* Light blue for Crew C */
        }
        .crew-D {
            background-color: #fff9c4 !important; /* Light yellow for Crew D */
        }
        /* Make sure the crew color is applied to all cells in the column */
        .gantt_grid_data .gantt_cell[data-column-name="progress"] {
            overflow: visible;
        }
    `;
    document.head.appendChild(style);

    // Scale change functions
    function setYearScale() {
        gantt.config.scale_unit = "year";
        gantt.config.date_scale = "%Y"; // Year on top row
        gantt.config.min_column_width = 50;
        gantt.config.subscales = [
            {
                unit: "quarter",
                step: 1,
                template: function(date) {
                    const quarter = Math.floor(date.getMonth() / 3) + 1;
                    return "Q" + quarter;
                }
            }
        ];
        gantt.render();
        setActiveButton("scale-year");
    }

    function setMonthScale() {
        gantt.config.scale_unit = "year";
        gantt.config.date_scale = "%Y"; // Year on top row
        gantt.config.min_column_width = 120;
        gantt.config.subscales = [
            {unit: "month", step: 1, date: "%F"} // Month on bottom row
        ];
        gantt.render();
        setActiveButton("scale-month");
    }

    function setWeekScale() {
        gantt.config.scale_unit = "month";
        gantt.config.date_scale = "%F"; // Month on top row
        gantt.config.min_column_width = 120;
        gantt.config.subscales = [
            {unit: "week", step: 1, date: "Week %W"} // Week number on bottom row
        ];
        gantt.render();
        setActiveButton("scale-week");
    }

    function setDayScale() {
        gantt.config.scale_unit = "month";
        gantt.config.date_scale = "%F"; // Month on top row
        gantt.config.min_column_width = 40;
        gantt.config.subscales = [
            {unit: "day", step: 1, date: "%d"} // Date on bottom row
        ];
        gantt.render();
        setActiveButton("scale-day");
    }

    function setHourScale() {
        gantt.config.scale_unit = "day";
        gantt.config.date_scale = "%d"; // Date on top row
        gantt.config.min_column_width = 60;
        gantt.config.subscales = [
            {unit: "hour", step: 1, date: "%H:%i"} // Hour on bottom row
        ];
        gantt.render();
        setActiveButton("scale-hour");
    }

    // Track current expansion level
    let currentExpansionLevel = null;

    // Hierarchy expansion functions
    function toggleExpansionLevel(level, buttonId) {
        // Check if there are special activities and update the work order button visibility
        const hasSpecialActivities = checkForSpecialActivities();
        const workOrderButton = document.getElementById('expand-work-order');

        if (hasSpecialActivities) {
            workOrderButton.style.display = 'inline-block';
        } else {
            workOrderButton.style.display = 'none';
        }

        // If clicking the same button that's already active, collapse one level up
        if (currentExpansionLevel === level) {
            if (level > 0) {
                // Collapse to one level up
                expandToLevel(level - 1);

                // Set the previous level button as active
                const previousLevelButtons = {
                    1: 'expand-name',
                    2: 'expand-stage',
                    3: 'expand-description',
                    4: 'expand-work-order'
                };

                setActiveHierarchyButton(previousLevelButtons[level]);
                currentExpansionLevel = level - 1;
            } else {
                // If at Name level, collapse all
                gantt.eachTask(function(task) {
                    task.$open = false;
                });
                gantt.render();

                // Remove active class from button
                document.getElementById(buttonId).classList.remove('active');
                currentExpansionLevel = null;
            }
        } else {
            // Expand to the specified level
            expandToLevel(level);
            setActiveHierarchyButton(buttonId);
            currentExpansionLevel = level;
        }
    }
    function expandToLevel(level) {
        // First collapse all
        gantt.eachTask(function(task) {
            task.$open = false;
        });

        // Then expand to the specified level
        gantt.eachTask(function(task) {
            const taskLevel = getTaskLevel(task);
            if (taskLevel < level) {  // Changed from <= to < to only expand parent levels
                task.$open = true;
            }
        });

        gantt.render();
    }

    function getTaskLevel(task) {
        let level = 0;
        let parent = task.parent;

        while (parent && parent !== 0) {
            level++;
            parent = gantt.getTask(parent).parent;
        }

        return level;
    }

    // Helper function to get parent task
    function getParentTask(task) {
        if (!task || !task.parent || task.parent === 0) {
            return null;
        }
        return gantt.getTask(task.parent);
    }

    // Add event listeners to scale buttons
    document.getElementById('scale-year').addEventListener('click', setYearScale);
    document.getElementById('scale-month').addEventListener('click', setMonthScale);
    document.getElementById('scale-week').addEventListener('click', setWeekScale);
    document.getElementById('scale-day').addEventListener('click', setDayScale);
    document.getElementById('scale-hour').addEventListener('click', setHourScale);

    // Add event listeners to hierarchy buttons
    document.getElementById('expand-name').addEventListener('click', function() {
        toggleExpansionLevel(0, 'expand-name'); // Technician/Name level (now level 0)
    });

    document.getElementById('expand-stage').addEventListener('click', function() {
        toggleExpansionLevel(1, 'expand-stage'); // Stage level (now level 1)
    });

    document.getElementById('expand-description').addEventListener('click', function() {
        toggleExpansionLevel(2, 'expand-description'); // Description level (now level 2)
    });

    document.getElementById('expand-work-order').addEventListener('click', function() {
        toggleExpansionLevel(3, 'expand-work-order'); // Work Order Number level (level 3 for special activities)
    });

    document.getElementById('expand-status').addEventListener('click', function() {
        // For regular activities, status is level 3
        // For special activities, status is level 4
        const hasSpecialActivities = checkForSpecialActivities();
        if (hasSpecialActivities) {
            toggleExpansionLevel(4, 'expand-status'); // Status level (level 4 for special activities)
        } else {
            toggleExpansionLevel(3, 'expand-status'); // Status level (level 3 for regular activities)
        }
    });

    // Function to check if there are any descriptions with multiple work orders in the current data
    function checkForSpecialActivities() {
        let hasMultipleWorkOrders = false;
        gantt.eachTask(function(task) {
            if (task.has_multiple_work_orders) {
                hasMultipleWorkOrders = true;
            }
        });
        return hasMultipleWorkOrders;
    }

    function setActiveHierarchyButton(buttonId) {
        document.querySelectorAll('.hierarchy-controls button').forEach(btn => {
            btn.classList.remove('active');
        });
        document.getElementById(buttonId).classList.add('active');
    }

    function setActiveButton(buttonId) {
        document.querySelectorAll('.time-scale-controls button').forEach(btn => {
            btn.classList.remove('active');
        });
        document.getElementById(buttonId).classList.add('active');
    }

    // Custom task class based on hierarchy level and status
    gantt.templates.task_class = function(start, end, task) {
        // Get the task level
        const level = getTaskLevel(task);

        // Add level-specific class
        let levelClass = `level_${level}`;

        // Add status class if available
        if (task.status) {
            levelClass += ` status_${task.status}`;
        }

        return levelClass;
    };

    // Initialize gantt
    gantt.init("gantt-container");

    // Fetch work orders from the API
    showLoadingIndicator(); // Show loading indicator before fetch
    fetch('/api/check-workorders')
        .then(response => response.json())
        .then(data => {
            updateLoadingProgress(50); // Update progress to 50% after data is received

            const workOrders = data.records || data;
            if (!workOrders || !workOrders.length) {
                hideLoadingIndicator(); // Hide indicator if no data
                document.getElementById('gantt-container').innerHTML =
                    '<div class="error-message">No work orders found</div>';
                return;
            }

            // Populate department filter dropdown
            const departmentFilter = document.getElementById('department-filter');
            const departments = new Set();

            // Collect unique departments
            workOrders.forEach(wo => {
                if (wo.owning_department) {
                    departments.add(wo.owning_department);
                }
            });

            // Add departments to dropdown
            Array.from(departments).sort().forEach((dept, index) => {
                const option = document.createElement('option');
                option.value = dept;
                option.textContent = dept;
                departmentFilter.appendChild(option);
            });

            // Store original data for filtering
            let originalWorkOrders = [...workOrders];

            // Add event listener for department filter
            departmentFilter.addEventListener('change', function() {
                const selectedDepartment = this.value;

                // Show loading indicator when trade is changed
                showLoadingIndicator();

                // Create a progress tracker
                let progressSteps = [
                    { percent: 0, message: "Starting..." },
                    { percent: 10, message: "Filtering data..." },
                    { percent: 30, message: "Preparing chart..." },
                    { percent: 50, message: "Building hierarchy..." },
                    { percent: 70, message: "Calculating timelines..." },
                    { percent: 90, message: "Rendering chart..." },
                    { percent: 100, message: "Complete" }
                ];

                let currentStep = 0;

                // Function to update to next progress step
                function nextProgressStep() {
                    if (currentStep < progressSteps.length) {
                        updateLoadingProgress(progressSteps[currentStep].percent);
                        currentStep++;
                        return progressSteps[currentStep - 1].percent;
                    }
                    return 100;
                }

                // Start progress
                nextProgressStep(); // 0%

                // Use setTimeout to allow the loading indicator to render before processing
                setTimeout(() => {
                    // Filter work orders
                    let filteredWorkOrders = originalWorkOrders.filter(wo => // Changed from const to let
                        wo.owning_department === selectedDepartment
                    );
                    nextProgressStep(); // 10%

                    setTimeout(() => {
                        // Prepare for chart building
                        nextProgressStep(); // 30%

                        setTimeout(() => {
                            // Build hierarchy
                            nextProgressStep(); // 50%

                            setTimeout(() => {
                                // Optimize timeline calculations for better performance
                                setTimeout(() => {
                                    // Calculate timelines
                                    nextProgressStep(); // 70%

                                    // Performance optimizations for timeline calculations

                                    // 1. Use a more efficient date parsing approach
                                    const parseDate = (dateStr) => {
                                        if (!dateStr) return new Date(0);
                                        // Use a direct approach instead of multiple parsing attempts
                                        const date = new Date(dateStr);
                                        return isNaN(date.getTime()) ? new Date() : date;
                                    };

                                    // 2. Pre-process dates once instead of repeatedly parsing them
                                    const workOrdersWithParsedDates = filteredWorkOrders.map(wo => ({
                                        ...wo,
                                        parsedDate: parseDate(wo.status_date)
                                    }));

                                    // 3. Group work orders by number for faster lookups
                                    const workOrdersByNumber = {};
                                    workOrdersWithParsedDates.forEach(wo => {
                                        const woNum = wo.work_order_number;
                                        if (!workOrdersByNumber[woNum]) {
                                            workOrdersByNumber[woNum] = [];
                                        }
                                        workOrdersByNumber[woNum].push(wo);
                                    });

                                    // Now use the optimized data for building the chart
                                    setTimeout(() => {
                                        // Render chart with optimized data
                                        buildGanttChartOptimized(workOrdersWithParsedDates, workOrdersByNumber);
                                        nextProgressStep(); // 90%

                                        // After chart is rebuilt, set expansion level to "Name" (assigned_to)
                                        setTimeout(() => {
                                            // Check if there are special activities and update the work order button visibility
                                            const hasSpecialActivities = checkForSpecialActivities();
                                            const workOrderButton = document.getElementById('expand-work-order');

                                            if (hasSpecialActivities) {
                                                workOrderButton.style.display = 'inline-block';
                                            } else {
                                                workOrderButton.style.display = 'none';
                                            }

                                            // Set the Name button as active
                                            setActiveHierarchyButton('expand-name');
                                            // Expand to Name level (level 0)
                                            expandToLevel(0);
                                            // Update current expansion level
                                            currentExpansionLevel = 0;

                                            // Complete
                                            nextProgressStep(); // 100%

                                            // Hide loading indicator when complete
                                            setTimeout(() => {
                                                hideLoadingIndicator();
                                            }, 300); // Show 100% for a moment before hiding
                                        }, 200);
                                    }, 50);
                                }, 50);
                            }, 50);
                        }, 50);
                    }, 50);
                }, 50);
            });

            // Initial build with filtered data for the first department in the list
            if (departments.size > 0) {
                updateLoadingProgress(75); // Update progress to 75% before building chart
                const firstDepartment = Array.from(departments).sort()[0];
                let initialFiltered = workOrders.filter(wo => // Changed from const to let
                    wo.owning_department === firstDepartment
                );

                // Store original data for filtering
                originalWorkOrders = [...workOrders];

                // Build the chart with the initial filtered data
                buildGanttChart(initialFiltered);

                // Set initial expansion level to "Name" (assigned_to)
                setTimeout(() => {
                    // Check if there are special activities and update the work order button visibility
                    const hasSpecialActivities = checkForSpecialActivities();
                    const workOrderButton = document.getElementById('expand-work-order');

                    if (hasSpecialActivities) {
                        workOrderButton.style.display = 'inline-block';
                    } else {
                        workOrderButton.style.display = 'none';
                    }

                    setActiveHierarchyButton('expand-name');
                    expandToLevel(0);
                    currentExpansionLevel = 0;
                    hideLoadingIndicator(); // Hide indicator after chart is built and expanded
                    updateLoadingProgress(100);
                }, 200);
            } else {
                hideLoadingIndicator(); // Hide indicator if no departments
            }
        })
        .catch(error => {
            hideLoadingIndicator(); // Hide indicator on error
            console.error('Error fetching work orders:', error);
            document.getElementById('gantt-container').innerHTML =
                `<div class="error-message">Error loading data: ${error.message}</div>`;
        });
});

// Function to calculate status percentage
function getStatusPercentage(status) {
    switch(status) {
        case 'DRAFT': return 0;
        case 'INPRG': return 0.25;
        case 'OPCOMP': return 0.5;
        case 'COMP': return 0.75;
        case 'CLOSE': return 1;
        default: return 0;
    }
}

// Function to build the gantt chart with given data
function buildGanttChart(workOrders) {
    // Clear existing chart
    gantt.clearAll();

    updateLoadingProgress(80); // Update progress to 80% when building chart

    // Sort work orders by status_date (earliest to latest)
    workOrders.sort((a, b) => {
        const dateA = a.status_date ? new Date(a.status_date) : new Date(0);
        const dateB = b.status_date ? new Date(b.status_date) : new Date(0);
        const timeA = isNaN(dateA.getTime()) ? 0 : dateA.getTime();
        const timeB = isNaN(dateB.getTime()) ? 0 : dateB.getTime();
        return timeA - timeB;
    });

    // Process data for hierarchical structure
    const tasks = {
        data: [],
        links: []
    };

    // Create unique IDs for each level
    let idCounter = 1;
    const technicianMap = {};
    const stageMap = {};
    const workOrderMap = {};

    // Track progress for rollup calculations
    const technicianProgress = {};
    const stageProgress = {};
    const workOrderProgress = {};

    // First pass: Create technician nodes (assigned_to) in sorted order
    const sortedTechnicians = [...new Set(workOrders.map(wo => wo.assigned_to || 'Unassigned'))]
        .map(tech => ({ name: tech, crew: workOrders.find(wo => wo.assigned_to === tech)?.crew || '' }))
        .sort((a, b) => {
            const crewOrder = ['A', 'B', 'C', 'D'];
            const aIndex = crewOrder.indexOf(a.crew);
            const bIndex = crewOrder.indexOf(b.crew);
            if (aIndex === bIndex) {
                return a.name.localeCompare(b.name);
            }
            return aIndex - bIndex;
        });

    sortedTechnicians.forEach(techInfo => {
        const tech = techInfo.name;
        const crew = techInfo.crew;

        const techId = idCounter++;
        technicianMap[tech] = techId;

        tasks.data.push({
            id: techId,
            text: tech,
            type: 'project',
            open: true,
            parent: 0,
            progress: 0, // Will be updated later
            crew: crew // Add crew information
        });

        // Initialize progress tracking
        technicianProgress[tech] = {
            sum: 0,
            count: 0
        };
    });

    // Collect all stages for each technician
    const techStages = {};
    workOrders.forEach(wo => {
        const tech = wo.assigned_to || 'Unassigned';
        const stage = wo.stage || 'No Stage';

        if (!techStages[tech]) {
            techStages[tech] = new Set();
        }
        techStages[tech].add(stage);
    });

    // Second pass: Create stage nodes (sorted alphabetically)
    Object.keys(techStages).forEach(tech => {
        // Convert set to array and sort alphabetically
        const stages = Array.from(techStages[tech]).sort();

        stages.forEach(stage => {
            const stageKey = `${tech}_${stage}`;

            if (!stageMap[stageKey]) {
                const stageId = idCounter++;
                stageMap[stageKey] = stageId;

                tasks.data.push({
                    id: stageId,
                    text: stage,
                    type: 'project',
                    open: true,
                    parent: technicianMap[tech],
                    progress: 0 // Will be updated later
                });

                // Initialize progress tracking
                stageProgress[stageKey] = {
                    sum: 0,
                    count: 0
                };
            }
        });
    });

    // First, collect all descriptions and their work order numbers
    const descriptionWorkOrders = {};

    workOrders.forEach(wo => {
        const tech = wo.assigned_to || 'Unassigned';
        const stage = wo.stage || 'No Stage';
        const workOrderNum = wo.work_order_number || 'Unknown';
        const description = wo.description || 'No Description';
        const stageKey = `${tech}_${stage}`;
        const descKey = `${stageKey}_${description}`;

        // Initialize the set if it doesn't exist
        if (!descriptionWorkOrders[descKey]) {
            descriptionWorkOrders[descKey] = new Set();
        }

        // Add this work order number to the set
        descriptionWorkOrders[descKey].add(workOrderNum);
    });

    // Third pass: Create work order description nodes with progress based on latest status
    // Create maps to track descriptions that need work order number level
    const descriptionMap = {};

    workOrders.forEach(wo => {
        const tech = wo.assigned_to || 'Unassigned';
        const stage = wo.stage || 'No Stage';
        const workOrderNum = wo.work_order_number || 'Unknown';
        const description = wo.description || 'No Description';
        const stageKey = `${tech}_${stage}`;
        const descKey = `${stageKey}_${description}`;

        // Check if this description has multiple work order numbers (2 or more)
        const hasMultipleWorkOrders = descriptionWorkOrders[descKey] && descriptionWorkOrders[descKey].size >= 2;

        // For descriptions with multiple work orders, use description as the key to group identical descriptions
        const workOrderKey = hasMultipleWorkOrders
            ? descKey
            : `${stageKey}_${workOrderNum}`;

        // Find the latest status for this work order
        const workOrderStatuses = workOrders.filter(o =>
            o.work_order_number === wo.work_order_number &&
            o.assigned_to === wo.assigned_to
        );

        let latestStatus = wo.status;
        let latestDate = wo.status_date ? new Date(wo.status_date) : new Date(0);

        workOrderStatuses.forEach(status => {
            const statusDate = status.status_date ? new Date(status.status_date) : new Date(0);
            if (statusDate > latestDate) {
                latestDate = statusDate;
                latestStatus = status.status;
            }
        });

        // Calculate progress based on latest status
        const progress = getStatusPercentage(latestStatus);

        if (!workOrderMap[workOrderKey]) {
            const workOrderId = idCounter++;
            workOrderMap[workOrderKey] = workOrderId;

            tasks.data.push({
                id: workOrderId,
                text: description,
                type: 'project',
                open: true,
                parent: stageMap[stageKey],
                progress: progress, // Set progress based on latest status
                work_order_number: hasMultipleWorkOrders ? null : workOrderNum, // Only add work_order_number for single work order descriptions
                has_multiple_work_orders: hasMultipleWorkOrders // Flag to indicate if this description has multiple work orders
            });

            // Add to stage progress
            if (stageProgress[stageKey]) {
                stageProgress[stageKey].sum += progress;
                stageProgress[stageKey].count++;
            }

            // For descriptions with multiple work orders, create a map to track them
            if (hasMultipleWorkOrders) {
                descriptionMap[workOrderKey] = {
                    id: workOrderId,
                    workOrders: new Set()
                };
            }
        }

        // For descriptions with multiple work orders, add this work order to the description's set of work orders
        if (hasMultipleWorkOrders && descriptionMap[workOrderKey]) {
            descriptionMap[workOrderKey].workOrders.add(workOrderNum);
        }
    });

    // Store the description map for use in the fourth pass
    window.ganttDescriptionMap = descriptionMap;

    // Fourth pass: Create status nodes with proper date ranges and progress
    // Create a map to store work order number nodes for descriptions with multiple work orders
    const workOrderNumberMap = {};

    workOrders.forEach((wo) => {
        // Skip CLOSE status - we'll use its date as the end date for COMP
        if (wo.status === 'CLOSE' || wo.status === 'DRAFT') {
            return; // Skip this iteration
        }

        const tech = wo.assigned_to || 'Unassigned';
        const stage = wo.stage || 'No Stage';
        const workOrderNum = wo.work_order_number || 'Unknown';
        const description = wo.description || 'No Description';
        const stageKey = `${tech}_${stage}`;
        const descKey = `${stageKey}_${description}`;

        // Check if this description has multiple work order numbers (2 or more)
        const hasMultipleWorkOrders = descriptionWorkOrders[descKey] && descriptionWorkOrders[descKey].size >= 2;

        // For descriptions with multiple work orders, use description as the key to group identical descriptions
        const workOrderKey = hasMultipleWorkOrders
            ? descKey
            : `${stageKey}_${workOrderNum}`;

        // Parse current status date
        let statusDate;
        try {
            if (wo.status_date) {
                // Handle different date formats
                if (typeof wo.status_date === 'string' && wo.status_date.includes('/')) {
                    // Format like "1/1/2023 12:00:00 AM"
                    const parts = wo.status_date.split(' ');
                    const dateParts = parts[0].split('/');
                    const timeParts = parts[1] ? parts[1].split(':') : [0, 0, 0];
                    const isPM = parts[2] && parts[2].toUpperCase() === 'PM';

                    let hours = parseInt(timeParts[0]);
                    if (isPM && hours < 12) hours += 12;
                    if (!isPM && hours === 12) hours = 0;

                    statusDate = new Date(
                        parseInt(dateParts[2]), // year
                        parseInt(dateParts[0]) - 1, // month (0-based)
                        parseInt(dateParts[1]), // day
                        hours, // hours
                        parseInt(timeParts[1] || 0), // minutes
                        parseInt(timeParts[2] || 0) // seconds
                    );
                } else {
                    // ISO format or already a Date object
                    statusDate = new Date(wo.status_date);
                }
            } else {
                statusDate = new Date(); // Default to current date if no status_date
            }
        } catch (e) {
            console.error('Error parsing date:', wo.status_date, e);
            statusDate = new Date(); // Fallback to current date
        }

        // Ensure we have a valid date
        if (isNaN(statusDate.getTime())) {
            console.error('Invalid date:', wo.status_date);
            statusDate = new Date(); // Fallback to current date
        }

        // Determine end date
        let endDate = new Date(statusDate);
        endDate.setDate(endDate.getDate() + 3); // Default: extend for 3 days

        // Look for the next status for this work order to set end date
        const nextStatus = workOrders.find(o =>
            o.work_order_number === wo.work_order_number &&
            o.status_date > wo.status_date
        );

        if (nextStatus) {
            endDate = new Date(nextStatus.status_date);
        }

        // Special handling for COMP status - use CLOSE date as end date if available
        if (wo.status === 'COMP') {
            const closeStatus = workOrders.find(o =>
                o.work_order_number === wo.work_order_number &&
                o.status === 'CLOSE'
            );
            if (closeStatus) {
                endDate = new Date(closeStatus.status_date);
            }
        }

        // Make sure the parent work order exists
        if (workOrderMap[workOrderKey]) {
            // For descriptions with multiple work orders, create a work_order_number level if it doesn't exist
            if (hasMultipleWorkOrders) {
                // Get the description node from the map
                const descInfo = window.ganttDescriptionMap[workOrderKey];

                if (descInfo) {
                    // Create a unique key for this work order number under this description
                    const workOrderNumberKey = `${workOrderKey}_${workOrderNum}`;

                    if (!workOrderNumberMap[workOrderNumberKey]) {
                        const workOrderNumberId = idCounter++;
                        workOrderNumberMap[workOrderNumberKey] = workOrderNumberId;

                        tasks.data.push({
                            id: workOrderNumberId,
                            text: workOrderNum, // Display the work order number
                            type: 'project',
                            open: true,
                            parent: descInfo.id, // Use the description node as parent
                            progress: 1, // Set to 100% progress
                            work_order_number: workOrderNum
                        });
                    }

                    // Add status node with work_order_number as parent
                    tasks.data.push({
                        id: `status_${wo.work_order_number}_${tech}_${wo.status}_${statusDate.getTime()}`,
                        text: wo.status || 'Unknown',
                        start_date: statusDate,
                        end_date: endDate,
                        status: wo.status,
                        parent: workOrderNumberMap[workOrderNumberKey], // Use work order number as parent
                        work_order_number: wo.work_order_number,
                        render: 'split', // Ensure the task is rendered
                        progress: 1 // Set all status nodes to 100% progress
                    });
                } else {
                    console.warn(`Description info not found for: ${workOrderKey}`);
                }
            } else {
                // For descriptions with a single work order, add status directly under description
                tasks.data.push({
                    id: `status_${wo.work_order_number}_${tech}_${wo.status}_${statusDate.getTime()}`,
                    text: wo.status || 'Unknown',
                    start_date: statusDate,
                    end_date: endDate,
                    status: wo.status,
                    parent: workOrderMap[workOrderKey], // Use description as parent
                    work_order_number: wo.work_order_number,
                    render: 'split', // Ensure the task is rendered
                    progress: 1 // Set all status nodes to 100% progress
                });
            }
        } else {
            console.warn(`Parent work order not found for: ${workOrderKey}`);
        }
    });

    // Calculate rollup progress for work order descriptions
    Object.keys(workOrderProgress).forEach(key => {
        const progress = workOrderProgress[key];
        if (progress.count > 0) {
            const avgProgress = progress.sum / progress.count;

            // Find the task and update its progress
            const [tech, stage, workOrderNum] = key.split('_');
            const stageKey = `${tech}_${stage}`;

            // Update work order progress
            const workOrderId = workOrderMap[key];
            const workOrderTask = tasks.data.find(t => t.id === workOrderId);
            if (workOrderTask) {
                workOrderTask.progress = avgProgress;
            }

            // Add to stage progress
            if (stageProgress[stageKey]) {
                stageProgress[stageKey].sum += avgProgress;
                stageProgress[stageKey].count++;
            }
        }
    });

    // Calculate rollup progress for stages
    Object.keys(stageProgress).forEach(key => {
        const progress = stageProgress[key];
        if (progress.count > 0) {
            const avgProgress = progress.sum / progress.count;

            // Find the task and update its progress
            const [tech, stage] = key.split('_');

            // Update stage progress
            const stageId = stageMap[key];
            const stageTask = tasks.data.find(t => t.id === stageId);
            if (stageTask) {
                stageTask.progress = avgProgress;
            }

            // Add to technician progress
            if (technicianProgress[tech]) {
                technicianProgress[tech].sum += avgProgress;
                technicianProgress[tech].count++;
            }
        }
    });

    // Calculate rollup progress for technicians
    Object.keys(technicianProgress).forEach(tech => {
        const progress = technicianProgress[tech];
        if (progress.count > 0) {
            const avgProgress = progress.sum / progress.count;

            // Update technician progress
            const techId = technicianMap[tech];
            const techTask = tasks.data.find(t => t.id === techId);
            if (techTask) {
                techTask.progress = avgProgress;
            }
        }
    });

    // Load data
    gantt.parse(tasks);

    updateLoadingProgress(95); // Update progress to 95% after data is loaded

    // After loading data, update the overview
    if (gantt.$ui && gantt.$ui.getView("overview")) {
        gantt.$ui.getView("overview").render();
    }
}

// Optimized version of buildGanttChart that uses pre-processed data
function buildGanttChartOptimized(workOrdersWithDates, workOrdersByNumber) {
    // Clear existing chart
    gantt.clearAll();

    updateLoadingProgress(80); // Update progress to 80% when building chart

    // Process data for hierarchical structure
    const tasks = {
        data: [],
        links: []
    };

    // Create unique IDs for each level
    let idCounter = 1;
    const technicianMap = {};
    const stageMap = {};
    const workOrderMap = {};

    // Track progress for rollup calculations
    const technicianProgress = {};
    const stageProgress = {};
    const workOrderProgress = {};

    // First pass: Create technician nodes (assigned_to) - use Set for faster unique value extraction
    const technicians = new Map();

    // Collect technicians with their crew information
    workOrdersWithDates.forEach(wo => {
        const tech = wo.assigned_to || 'Unassigned';
        const crew = wo.crew || '';

        if (!technicians.has(tech)) {
            technicians.set(tech, { name: tech, crew: crew });
        }
    });

    // Sort technicians by crew and then by name
    const sortedTechnicians = Array.from(technicians.values())
        .sort((a, b) => {
            const crewOrder = ['A', 'B', 'C', 'D'];
            const aIndex = crewOrder.indexOf(a.crew);
            const bIndex = crewOrder.indexOf(b.crew);
            if (aIndex === bIndex) {
                return a.name.localeCompare(b.name);
            }
            return aIndex - bIndex;
        });

    // Create technician nodes
    sortedTechnicians.forEach(techInfo => {
        const tech = techInfo.name;
        const crew = techInfo.crew;

        const techId = idCounter++;
        technicianMap[tech] = techId;

        tasks.data.push({
            id: techId,
            text: tech,
            type: 'project',
            open: true,
            parent: 0,
            progress: 0, // Will be updated later
            crew: crew // Add crew information
        });

        // Initialize progress tracking
        technicianProgress[tech] = {
            sum: 0,
            count: 0
        };
    });

    // Collect all stages for each technician - use Map for faster lookups
    const techStagesMap = new Map();

    workOrdersWithDates.forEach(wo => {
        const tech = wo.assigned_to || 'Unassigned';
        const stage = wo.stage || 'No Stage';

        if (!techStagesMap.has(tech)) {
            techStagesMap.set(tech, new Set());
        }
        techStagesMap.get(tech).add(stage);
    });

    // Second pass: Create stage nodes (sorted alphabetically)
    techStagesMap.forEach((stages, tech) => {
        // Convert set to array and sort alphabetically
        Array.from(stages).sort().forEach(stage => {
            const stageKey = `${tech}_${stage}`;

            if (!stageMap[stageKey]) {
                const stageId = idCounter++;
                stageMap[stageKey] = stageId;

                tasks.data.push({
                    id: stageId,
                    text: stage,
                    type: 'project',
                    open: true,
                    parent: technicianMap[tech],
                    progress: 0 // Will be updated later
                });

                // Initialize progress tracking
                stageProgress[stageKey] = {
                    sum: 0,
                    count: 0
                };
            }
        });
    });

    // First, collect all descriptions and their work order numbers
    const descriptionWorkOrders = {};

    workOrdersWithDates.forEach(wo => {
        const tech = wo.assigned_to || 'Unassigned';
        const stage = wo.stage || 'No Stage';
        const workOrderNum = wo.work_order_number || 'Unknown';
        const description = wo.description || 'No Description';
        const stageKey = `${tech}_${stage}`;
        const descKey = `${stageKey}_${description}`;

        // Initialize the set if it doesn't exist
        if (!descriptionWorkOrders[descKey]) {
            descriptionWorkOrders[descKey] = new Set();
        }

        // Add this work order number to the set
        descriptionWorkOrders[descKey].add(workOrderNum);
    });

    // Third pass: Create work order description nodes with progress based on latest status
    // Create maps to track descriptions that need work order number level
    const descriptionMap = {};

    workOrdersWithDates.forEach(wo => {
        const tech = wo.assigned_to || 'Unassigned';
        const stage = wo.stage || 'No Stage';
        const workOrderNum = wo.work_order_number || 'Unknown';
        const description = wo.description || 'No Description';
        const stageKey = `${tech}_${stage}`;
        const descKey = `${stageKey}_${description}`;

        // Check if this description has multiple work order numbers (2 or more)
        const hasMultipleWorkOrders = descriptionWorkOrders[descKey] && descriptionWorkOrders[descKey].size >= 2;

        // For descriptions with multiple work orders, use description as the key to group identical descriptions
        const workOrderKey = hasMultipleWorkOrders
            ? descKey
            : `${stageKey}_${workOrderNum}`;

        // Find the latest status for this work order
        const workOrderStatuses = workOrdersByNumber[wo.work_order_number] || [];

        let latestStatus = wo.status;
        let latestDate = wo.parsedDate;

        workOrderStatuses.forEach(status => {
            if (status.parsedDate > latestDate) {
                latestDate = status.parsedDate;
                latestStatus = status.status;
            }
        });

        // Calculate progress based on latest status
        const progress = getStatusPercentage(latestStatus);

        if (!workOrderMap[workOrderKey]) {
            const workOrderId = idCounter++;
            workOrderMap[workOrderKey] = workOrderId;

            tasks.data.push({
                id: workOrderId,
                text: description,
                type: 'project',
                open: true,
                parent: stageMap[stageKey],
                progress: progress, // Set progress based on latest status
                work_order_number: hasMultipleWorkOrders ? null : workOrderNum, // Only add work_order_number for single work order descriptions
                has_multiple_work_orders: hasMultipleWorkOrders // Flag to indicate if this description has multiple work orders
            });

            // Add to stage progress
            if (stageProgress[stageKey]) {
                stageProgress[stageKey].sum += progress;
                stageProgress[stageKey].count++;
            }

            // For descriptions with multiple work orders, create a map to track them
            if (hasMultipleWorkOrders) {
                descriptionMap[workOrderKey] = {
                    id: workOrderId,
                    workOrders: new Set()
                };
            }
        }

        // For descriptions with multiple work orders, add this work order to the description's set of work orders
        if (hasMultipleWorkOrders && descriptionMap[workOrderKey]) {
            descriptionMap[workOrderKey].workOrders.add(workOrderNum);
        }
    });

    // Store the description map for use in the fourth pass
    window.ganttDescriptionMap = descriptionMap;

    // Fourth pass: Create status nodes with proper date ranges and progress
    // Create a map to store work order number nodes for descriptions with multiple work orders
    const workOrderNumberMap = {};

    workOrdersWithDates.forEach((wo) => {
        // Skip CLOSE status - we'll use its date as the end date for COMP
        if (wo.status === 'CLOSE' || wo.status === 'DRAFT') {
            return; // Skip this iteration
        }

        const tech = wo.assigned_to || 'Unassigned';
        const stage = wo.stage || 'No Stage';
        const workOrderNum = wo.work_order_number || 'Unknown';
        const description = wo.description || 'No Description';
        const stageKey = `${tech}_${stage}`;
        const descKey = `${stageKey}_${description}`;

        // Check if this description has multiple work order numbers (2 or more)
        const hasMultipleWorkOrders = descriptionWorkOrders[descKey] && descriptionWorkOrders[descKey].size >= 2;

        // For descriptions with multiple work orders, use description as the key to group identical descriptions
        const workOrderKey = hasMultipleWorkOrders
            ? descKey
            : `${stageKey}_${workOrderNum}`;

        // Determine end date
        let endDate = new Date(wo.parsedDate);
        endDate.setDate(endDate.getDate() + 3); // Default: extend for 3 days

        // Look for the next status for this work order to set end date
        const nextStatusIndex = workOrdersByNumber[workOrderNum]?.findIndex(w => w.parsedDate > wo.parsedDate);

        if (nextStatusIndex !== -1 && nextStatusIndex !== undefined) {
            endDate = workOrdersByNumber[workOrderNum][nextStatusIndex].parsedDate;
        }

        // Special handling for COMP status - use CLOSE date as end date if available
        if (wo.status === 'COMP') {
            const closeStatus = workOrdersByNumber[workOrderNum]?.find(w => w.status === 'CLOSE');
            if (closeStatus) {
                endDate = closeStatus.parsedDate;
            }
        }

        // Make sure the parent work order exists
        if (workOrderMap[workOrderKey]) {
            // For descriptions with multiple work orders, create a work_order_number level if it doesn't exist
            if (hasMultipleWorkOrders) {
                // Get the description node from the map
                const descInfo = window.ganttDescriptionMap[workOrderKey];

                if (descInfo) {
                    // Create a unique key for this work order number under this description
                    const workOrderNumberKey = `${workOrderKey}_${workOrderNum}`;

                    if (!workOrderNumberMap[workOrderNumberKey]) {
                        const workOrderNumberId = idCounter++;
                        workOrderNumberMap[workOrderNumberKey] = workOrderNumberId;

                        tasks.data.push({
                            id: workOrderNumberId,
                            text: workOrderNum, // Display the work order number
                            type: 'project',
                            open: true,
                            parent: descInfo.id, // Use the description node as parent
                            progress: 1, // Set to 100% progress
                            work_order_number: workOrderNum
                        });
                    }

                    // Add status node with work_order_number as parent
                    tasks.data.push({
                        id: `status_${wo.work_order_number}_${tech}_${wo.status}_${wo.parsedDate.getTime()}`,
                        text: wo.status || 'Unknown',
                        start_date: wo.parsedDate,
                        end_date: endDate,
                        status: wo.status,
                        parent: workOrderNumberMap[workOrderNumberKey], // Use work order number as parent
                        work_order_number: wo.work_order_number,
                        render: 'split', // Ensure the task is rendered
                        progress: 1 // Set all status nodes to 100% progress
                    });
                } else {
                    console.warn(`Description info not found for: ${workOrderKey}`);
                }
            } else {
                // For descriptions with a single work order, add status directly under description
                tasks.data.push({
                    id: `status_${wo.work_order_number}_${tech}_${wo.status}_${wo.parsedDate.getTime()}`,
                    text: wo.status || 'Unknown',
                    start_date: wo.parsedDate,
                    end_date: endDate,
                    status: wo.status,
                    parent: workOrderMap[workOrderKey], // Use description as parent
                    work_order_number: wo.work_order_number,
                    render: 'split', // Ensure the task is rendered
                    progress: 1 // Set all status nodes to 100% progress
                });
            }
        } else {
            console.warn(`Parent work order not found for: ${workOrderKey}`);
        }
    });

    // Calculate rollup progress for work order descriptions
    Object.keys(workOrderProgress).forEach(key => {
        const progress = workOrderProgress[key];
        if (progress.count > 0) {
            const avgProgress = progress.sum / progress.count;

            // Find the task and update its progress
            const [tech, stage, workOrderNum] = key.split('_');
            const stageKey = `${tech}_${stage}`;

            // Update work order progress
            const workOrderId = workOrderMap[key];
            const workOrderTask = tasks.data.find(t => t.id === workOrderId);
            if (workOrderTask) {
                workOrderTask.progress = avgProgress;
            }

            // Add to stage progress
            if (stageProgress[stageKey]) {
                stageProgress[stageKey].sum += avgProgress;
                stageProgress[stageKey].count++;
            }
        }
    });

    // Calculate rollup progress for stages
    Object.keys(stageProgress).forEach(key => {
        const progress = stageProgress[key];
        if (progress.count > 0) {
            const avgProgress = progress.sum / progress.count;

            // Find the task and update its progress
            const [tech, stage] = key.split('_');

            // Update stage progress
            const stageId = stageMap[key];
            const stageTask = tasks.data.find(t => t.id === stageId);
            if (stageTask) {
                stageTask.progress = avgProgress;
            }

            // Add to technician progress
            if (technicianProgress[tech]) {
                technicianProgress[tech].sum += avgProgress;
                technicianProgress[tech].count++;
            }
        }
    });

    // Calculate rollup progress for technicians
    Object.keys(technicianProgress).forEach(tech => {
        const progress = technicianProgress[tech];
        if (progress.count > 0) {
            const avgProgress = progress.sum / progress.count;

            // Update technician progress
            const techId = technicianMap[tech];
            const techTask = tasks.data.find(t => t.id === techId);
            if (techTask) {
                techTask.progress = avgProgress;
            }
        }
    });

    // Load data
    gantt.parse(tasks);

    updateLoadingProgress(95); // Update progress to 95% after data is loaded

    // After loading data, update the overview
    if (gantt.$ui && gantt.$ui.getView("overview")) {
        gantt.$ui.getView("overview").render();
    }
}
