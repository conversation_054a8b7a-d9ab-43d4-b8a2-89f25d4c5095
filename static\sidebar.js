// Wrap everything in an immediately invoked function expression (IIFE)
// to avoid polluting the global namespace
(function() {
    // Add this function to handle logout confirmation
    function confirmLogout(event) {
        event.preventDefault();
        if (confirm('Are you sure you want to logout?')) {
            window.location.href = '/logout';
        }
    }

    // Make confirmLogout available globally
    window.confirmLogout = confirmLogout;

    // Wait for DOM to be fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        const sidebar = document.querySelector(".sidebar");
        const sidebarToggler = document.querySelector(".sidebar-toggler");
        const menuToggler = document.querySelector(".menu-toggler");
        const container = document.querySelector('.container');

        // If elements don't exist, exit early
        if (!sidebar) {
            console.warn("Sidebar element not found");
            return;
        }

        // Ensure these heights match the CSS sidebar height values
        let collapsedSidebarHeight = "56px"; // Height in mobile view (collapsed)
        let fullSidebarHeight = "calc(100vh - 32px)"; // Height in larger screen

        // Toggle sidebar's collapsed state
        if (sidebarToggler) {
            sidebarToggler.addEventListener("click", () => {
                sidebar.classList.toggle("collapsed");
                
                // Also toggle the container class
                if (container) {
                    container.classList.toggle('sidebar-collapsed');
                }
            });
        }

        // Update sidebar height and menu toggle text
        const toggleMenu = (isMenuActive) => {
            if (!menuToggler) return;
            
            sidebar.style.height = isMenuActive ? `${sidebar.scrollHeight}px` : collapsedSidebarHeight;
            const menuSpan = menuToggler.querySelector("span");
            if (menuSpan) {
                menuSpan.innerText = isMenuActive ? "close" : "menu";
            }
        }

        // Toggle menu-active class and adjust height
        if (menuToggler) {
            menuToggler.addEventListener("click", () => {
                toggleMenu(sidebar.classList.toggle("menu-active"));
            });
        }

        // Adjust sidebar height on window resize
        window.addEventListener("resize", () => {
            if (window.innerWidth >= 1024) {
                sidebar.style.height = fullSidebarHeight;
            } else {
                sidebar.classList.remove("collapsed");
                sidebar.style.height = "auto";
                if (menuToggler) {
                    toggleMenu(sidebar.classList.contains("menu-active"));
                }
            }
        });

        // Check if sidebar is already collapsed on page load (if you're storing the state)
        if (sidebar.classList.contains('collapsed') && container) {
            container.classList.add('sidebar-collapsed');
        }
    });
});
