import pandas as pd
import sqlite3
import os
import shutil
import time
import sys

def main():
    # Define file paths
    template_file = os.path.abspath(os.path.join('data', 'Facility Flex Chart template.xlsx'))
    output_file = os.path.abspath(os.path.join('data', 'Facility Flex Chart LOCAL.xlsx'))
    database_file = os.path.abspath(os.path.join('data', 'Data.db'))
    
    # Check if files exist
    if not os.path.exists(template_file):
        print(f"Error: Template file '{template_file}' not found.")
        return
    
    if not os.path.exists(database_file):
        print(f"Error: Database file '{database_file}' not found.")
        return
    
    try:
        print("Starting process...")
        
        # Make sure directory exists
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # Create a connection to the SQLite database
        print("Connecting to database...")
        conn = sqlite3.connect(database_file)
        
        # Read the WorkOrder table into a pandas DataFrame
        print("Querying WorkOrder table...")
        query = """
            SELECT w.*
            FROM WorkOrder w
            JOIN (
                SELECT work_order_number, MAX(status_date) AS max_status_date
                FROM WorkOrder
                GROUP BY work_order_number
            ) latest
            ON w.work_order_number = latest.work_order_number
            AND w.status_date = latest.max_status_date
        """
        work_orders = pd.read_sql_query(query, conn)
        print(f"Retrieved {len(work_orders)} records from WorkOrder table.")
        
        # Close the database connection
        conn.close()
        
        # Create a temporary CSV file to hold the data
        temp_csv = os.path.abspath(os.path.join('data', 'temp_workorder_data.csv'))
        print(f"Creating temporary CSV file: {temp_csv}")
        work_orders.to_csv(temp_csv, index=False)
        
        # Use win32com to interact with Excel directly
        print("Initializing Excel application...")
        try:
            import win32com.client
            excel = win32com.client.Dispatch("Excel.Application")
            excel.DisplayAlerts = False  # Disable alerts
            excel.Visible = False  # Run Excel in the background
            
            # Make a copy of the template first
            print(f"Making a copy of template to: {output_file}")
            if os.path.exists(output_file):
                os.remove(output_file)
            shutil.copy2(template_file, output_file)
            
            # Open the copied file
            print(f"Opening the copied file: {output_file}")
            workbook = excel.Workbooks.Open(output_file)
            
            # Find the AppData sheet
            app_data_sheet = None
            for sheet in workbook.Sheets:
                if sheet.Name == "AppData":
                    app_data_sheet = sheet
                    print("Found existing AppData sheet.")
                    break
            
            if app_data_sheet is None:
                print("Error: AppData sheet not found in template.")
                workbook.Close(SaveChanges=False)
                excel.Quit()
                return
            
            # Find the AppDataTable
            list_objects = app_data_sheet.ListObjects
            app_data_table = None
            
            # Check if there are any tables in the sheet
            if list_objects.Count > 0:
                for table_idx in range(1, list_objects.Count + 1):
                    table = list_objects.Item(table_idx)
                    if table.Name == "AppDataTable":
                        app_data_table = table
                        print("Found existing AppDataTable.")
                        break
            
            if app_data_table is None:
                print("Error: AppDataTable not found in sheet.")
                workbook.Close(SaveChanges=False)
                excel.Quit()
                return
            
            # Delete the table but keep the headers if possible
            print("Removing existing table while preserving structure...")
            # Instead of modifying the table, let's delete it and create a new one
            table_location = app_data_table.Range.Address
            app_data_table.Delete()
            
            # Import the CSV data to the old table location
            print("Importing data from CSV...")
            # Excel constants for import
            xlDelimited = 1
            xlTextQualifierDoubleQuote = 1
            
            # Define the destination range starting at the original table location
            start_cell_address = table_location.split(":")[0]
            destination_range = app_data_sheet.Range(start_cell_address)
            
            # Use Text Import method to import the CSV
            excel.Workbooks.OpenText(
                Filename=temp_csv,
                Origin=437,  # ASCII
                StartRow=1,
                DataType=xlDelimited,
                TextQualifier=xlTextQualifierDoubleQuote,
                ConsecutiveDelimiter=False,
                Tab=False,
                Semicolon=False,
                Comma=True,
                Space=False,
                Other=False
            )
            
            # Get the active workbook which should be our CSV
            csv_workbook = excel.ActiveWorkbook
            csv_worksheet = csv_workbook.Sheets(1)
            
            # Copy all the data
            all_data_range = csv_worksheet.UsedRange
            all_data_range.Copy()
            
            # Paste to our destination
            destination_range.PasteSpecial()
            
            # Close the CSV workbook without saving
            csv_workbook.Close(SaveChanges=False)
            
            # Create a new table with the pasted data
            data_range = app_data_sheet.Range(
                destination_range,
                app_data_sheet.Cells(
                    destination_range.Row + work_orders.shape[0],
                    destination_range.Column + work_orders.shape[1] - 1
                )
            )
            
            # Create a new table with the pasted data
            print("Creating new AppDataTable with the imported data...")
            new_table = list_objects.Add(
                SourceType=1,  # xlSrcRange
                Source=data_range, 
                XlListObjectHasHeaders=True,
                TableStyleName="TableStyleMedium2"
            )
            new_table.Name = "AppDataTable"
            
            # Save the workbook
            print("Saving workbook...")
            workbook.Save()
            workbook.Close()
            excel.Quit()
            
            # Clean up the temporary CSV file
            if os.path.exists(temp_csv):
                os.remove(temp_csv)
                print("Removed temporary CSV file.")
            
            print(f"Successfully updated '{output_file}' with WorkOrder data.")
            
        except ImportError:
            print("Error: win32com module not found. Please install it with:")
            print("pip install pywin32")
            return
        except Exception as e:
            print(f"An error occurred while working with Excel: {str(e)}")
            try:
                if 'csv_workbook' in locals():
                    csv_workbook.Close(SaveChanges=False)
                if 'workbook' in locals():
                    workbook.Close(SaveChanges=False)
                excel.Quit()
            except:
                pass
            # Clean up the temporary CSV file if it exists
            if os.path.exists(temp_csv):
                try:
                    os.remove(temp_csv)
                except:
                    pass
            raise
            
    except Exception as e:
        print(f"An error occurred: {str(e)}")
        # Make sure Excel is closed
        try:
            if 'excel' in locals():
                excel.Quit()
        except:
            pass

if __name__ == "__main__":
    start_time = time.time()
    main()
    print(f"Script completed in {time.time() - start_time:.2f} seconds")