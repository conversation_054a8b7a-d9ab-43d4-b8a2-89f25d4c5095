{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting export process...\n", "Connecting to database...\n", "Querying OrgChart table...\n", "Retrieved 119 records from OrgChart table.\n", "Exporting data to Excel: c:\\Users\\<USER>\\Desktop\\MyWebsite\\UltiumApp\\data\\OrgChart_Export.xlsx\n", "Successfully exported OrgChart data to 'c:\\Users\\<USER>\\Desktop\\MyWebsite\\UltiumApp\\data\\OrgChart_Export.xlsx'.\n"]}], "source": ["import pandas as pd\n", "import sqlite3\n", "import os\n", "\n", "def export_org_chart_to_excel():\n", "    # Define file paths\n", "    database_file = os.path.abspath(os.path.join('data', 'Data.db'))\n", "    output_file = os.path.abspath(os.path.join('data', 'OrgChart_Export.xlsx'))\n", "    \n", "    # Check if database file exists\n", "    if not os.path.exists(database_file):\n", "        print(f\"Error: Database file '{database_file}' not found.\")\n", "        return\n", "    \n", "    try:\n", "        print(\"Starting export process...\")\n", "        \n", "        # Make sure directory exists\n", "        os.makedirs(os.path.dirname(output_file), exist_ok=True)\n", "        \n", "        # Create a connection to the SQLite database\n", "        print(\"Connecting to database...\")\n", "        conn = sqlite3.connect(database_file)\n", "        \n", "        # Read the OrgChart table into a pandas DataFrame\n", "        print(\"Querying OrgChart table...\")\n", "        query = \"SELECT * FROM OrgChart\"\n", "        org_chart_data = pd.read_sql_query(query, conn)\n", "        print(f\"Retrieved {len(org_chart_data)} records from OrgChart table.\")\n", "        \n", "        # Close the database connection\n", "        conn.close()\n", "        \n", "        # Export to Excel\n", "        print(f\"Exporting data to Excel: {output_file}\")\n", "        org_chart_data.to_excel(output_file, index=False)\n", "        \n", "        print(f\"Successfully exported OrgChart data to '{output_file}'.\")\n", "        \n", "    except Exception as e:\n", "        print(f\"An error occurred: {str(e)}\")\n", "\n", "# Execute the function\n", "export_org_chart_to_excel()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.6"}}, "nbformat": 4, "nbformat_minor": 2}