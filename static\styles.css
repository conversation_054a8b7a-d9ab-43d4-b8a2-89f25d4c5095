/* Main container styling */
.container {
    margin-left: 290px; /* Sidebar width (270px) + sidebar margin (16px) + extra space */
    padding: 20px;
    transition: margin-left 0.4s ease;
    width: calc(100% - 290px); /* Ensure container takes remaining width */
}

/* When sidebar is collapsed */
.container.sidebar-collapsed {
    margin-left: 105px; /* Collapsed sidebar width (85px) + sidebar margin (16px) + extra space */
    width: calc(100% - 105px); /* Adjust width when sidebar is collapsed */
}

/* Table styling */
.table-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow-x: auto; /* Added to handle table overflow on smaller screens */
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

thead th {
    background-color: #f8f9fa;
    color: #333;
    font-weight: 600;
}

/* Form elements styling */
select, input[type="text"], input[type="date"] {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
}

/* Button styling */
.btn-add, .btn-delete {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.btn-add {
    background-color: #28a745;
    color: white;
}

.btn-delete {
    background-color: #dc3545;
    color: white;
}

.btn-add:hover {
    background-color: #218838;
}

.btn-delete:hover {
    background-color: #c82333;
}

/* Filter inputs styling */
.filter-row input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

/* Page header styling */
.page-header {
    margin-bottom: 20px;
}

.page-header h1 {
    color: #151A2D;
    font-size: 24px;
    font-weight: 600;
}

/* Responsive design */
@media (max-width: 1024px) {
    .container, .container.sidebar-collapsed {
        margin-left: 0;
        margin-top: 70px; /* Height of mobile sidebar + margin */
        padding: 15px;
        width: 100%; /* Take full width on mobile */
    }

    .table-container {
        padding: 15px 10px;
    }

    table {
        min-width: 800px; /* Ensure table doesn't get too squeezed */
    }
}

/* Upload section styling */
.upload-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin: 20px 0;
}

.upload-section h2 {
    margin-bottom: 15px;
}

#uploadBtn {
    margin: 10px 0;
    padding: 8px 16px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

#uploadBtn:hover {
    background-color: #45a049;
}

#uploadStatus {
    margin-top: 10px;
    padding: 10px;
    border-radius: 4px;
}

.table-container {
    margin-top: 2rem;
    overflow-x: auto;
}

#workOrderTable {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

#workOrderTable th,
#workOrderTable td {
    padding: 8px;
    border: 1px solid #ddd;
    text-align: left;
}

#workOrderTable th {
    background-color: #f4f4f4;
}

.table-filter {
    width: 100%;
    padding: 4px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.filter-row th {
    padding: 4px;
    background-color: #fff;
}

#workOrderTable tbody tr:nth-child(even) {
    background-color: #f9f9f9;
}

#workOrderTable tbody tr:hover {
    background-color: #f5f5f5;
}

.btn-secondary {
    margin: 10px 0;
    padding: 8px 16px;
    background-color: #2196F3;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-left: 10px;
}

.btn-secondary:hover {
    background-color: #1976D2;
}


