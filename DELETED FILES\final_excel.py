import os
import sqlite3
import pandas as pd
import openpyxl
from openpyxl import load_workbook

# Define paths
data_dir = 'data'
template_path = os.path.join(data_dir, 'Facility Flex Chart template.xlsx')
output_path = os.path.join(data_dir, 'Facility Flex Chart LOCAL.xlsx')
db_path = os.path.join(data_dir, 'Data.db')

# Create data directory if it doesn't exist
os.makedirs(data_dir, exist_ok=True)

# Connect to the database
conn = sqlite3.connect(db_path)

# Get the data from the WorkOrder table
df = pd.read_sql_query("SELECT * FROM WorkOrder", conn)

# Close the database connection
conn.close()

# Create a new Excel file with just the WorkOrder data
df.to_excel(output_path, sheet_name='AppData', index=False)

print(f"Excel file created successfully: {output_path}")
