import sqlite3
from flask import jsonify, request

def get_db_connection():
    conn = sqlite3.connect('Data.db')
    conn.row_factory = sqlite3.Row
    return conn

@app.route('/api/org-chart', methods=['GET'])
def get_org_chart():
    conn = get_db_connection()
    try:
        cursor = conn.cursor()
        cursor.execute('''
            SELECT id, parentId, name, position, department, email, phone, image
            FROM OrgChart
        ''')
        rows = cursor.fetchall()
        return jsonify([dict(row) for row in rows])
    finally:
        conn.close()

@app.route('/api/org-chart', methods=['POST'])
def update_org_chart():
    conn = get_db_connection()
    try:
        data = request.json
        cursor = conn.cursor()

        # Clear existing data
        cursor.execute('DELETE FROM OrgChart')

        # Insert new data
        cursor.execute('''
            INSERT INTO OrgChart (id, parentId, name, position, department, email, phone, image)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            data['id'],
            data['parentId'],
            data['name'],
            data['position'],
            data['department'],
            data['email'],
            data['phone'],
            data['image']
        ))

        conn.commit()
        return jsonify({'success': True})
    finally:
        conn.close()

# Initialize the database table
def init_org_chart_table():
    conn = get_db_connection()
    try:
        cursor = conn.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS OrgChart (
                id TEXT PRIMARY KEY,
                parentId TEXT,
                name TEXT NOT NULL,
                position TEXT NOT NULL,
                department TEXT NOT NULL,
                email TEXT,
                phone TEXT,
                image TEXT
            )
        ''')

        # Check if table is empty and insert root node if it is
        cursor.execute('SELECT COUNT(*) FROM OrgChart')
        if cursor.fetchone()[0] == 0:
            cursor.execute('''
                INSERT INTO OrgChart (id, parentId, name, position, department, email, phone, image)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                "1",
                "",
                "Ultium Cells Facility",
                "",
                "",
                "",
                "",
                "static/images/Ultium-New-Logo-icon.png"
            ))

        conn.commit()
    finally:
        conn.close()

# Call this during application startup
init_org_chart_table()