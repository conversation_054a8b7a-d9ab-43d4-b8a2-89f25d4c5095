# to run: python -m waitress --listen=0.0.0.0:8080 project:app
# access here: http://10.95.20.238:8080/

from turtle import pd
from flask import Flask, request, jsonify, render_template, session, redirect, url_for, flash, send_file
from ldap3 import Server, Connection, ALL
from functools import wraps
import sqlite3
import os
import logging
import pandas as pd4
from openpyxl import load_workbook
import tempfile
from datetime import datetime
import io

# Initialize the Flask application
app = Flask(__name__)
app.secret_key = "os.environ.get('SECRET_KEY', os.urandom(32))"

# Set up logging
logging.basicConfig(level=logging.DEBUG)

# Database setup
DB_PATH = os.path.join('Data', 'Data.db')
os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)

# LDAP Configuration
LDAP_SERVER = '10.95.5.42'
LDAP_DOMAIN = 'LGENSOL'
LDAP_BASE_DN = 'DC=LGENSOL,DC=com'

# Add this near the top of the file, where other paths are defined
FLEX_CHART_PATH = os.path.join('data', 'Facility Flex Chart LOCAL.xlsx')

# Make sure the data directory exists
os.makedirs(os.path.dirname(FLEX_CHART_PATH), exist_ok=True)

def get_db_connection():
    """Create a database connection and return the connection"""
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row  # This enables column access by name
    return conn

def ldap_authenticate(username, password):
    try:
        # Initialize server
        server = Server(LDAP_SERVER, get_info=ALL)
        user_dn = f'{username}@{LDAP_DOMAIN}'

        # Attempt to bind with credentials
        conn = Connection(server, user=user_dn, password=password)
        if conn.bind():
            return True
        return False
    except Exception as e:
        logging.error(f"LDAP Error: {str(e)}")
        flash('LDAP server is unavailable')
        return False

def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'username' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']

        # First check if user exists in Authorization table
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM Authorization WHERE employee_login = ?", (username,))
        authorized_user = cursor.fetchone()
        conn.close()

        if not authorized_user:
            flash('You are not authorized or not in facilities to access this system.')
            return redirect(url_for('login'))

        # If user is authorized, proceed with LDAP authentication
        if ldap_authenticate(username, password):
            session['username'] = username
            # Store the user's access level in the session
            session['access_level'] = authorized_user[3]  # access_level is the 4th column
            session['department'] = authorized_user[1]    # department is the 2nd column
            return redirect(url_for('index'))
        else:
            flash('Invalid credentials')
            return redirect(url_for('login'))

    return render_template('login.html')

@app.route('/logout')
def logout():
    session.pop('username', None)
    return redirect(url_for('login'))

# Protect routes that require authentication
@app.route('/')
@login_required
def index():
    return render_template('dashboard.html')

@app.route('/training')
@login_required
def training():
    return render_template('training.html')

@app.route('/api/upload-workorder', methods=['POST'])
def upload_workorder():
    if 'file' not in request.files:
        return jsonify({'success': False, 'error': 'No file uploaded'})

    file = request.files['file']
    if file.filename == '':
        return jsonify({'success': False, 'error': 'No file selected'})

    if not file.filename.endswith('.xlsx'):
        return jsonify({'success': False, 'error': 'File must be an Excel (.xlsx) file'})

    try:
        # Log the file details
        logging.debug(f"Processing file: {file.filename}")

        # Read the Excel file
        new_df = pd.read_excel(file)
        logging.debug(f"DataFrame columns: {new_df.columns.tolist()}")
        logging.debug(f"New records shape: {new_df.shape}")

        # Connect to the database
        logging.debug(f"Connecting to database at: {os.path.abspath(DB_PATH)}")
        conn = sqlite3.connect(DB_PATH)

        # Drop existing table and recreate without UNIQUE constraint
        conn.execute('DROP TABLE IF EXISTS WorkOrder')
        conn.execute('''CREATE TABLE WorkOrder (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            site TEXT,
            process TEXT,
            factory TEXT,
            owning_department TEXT,
            location TEXT,
            equipment_id TEXT,
            equipment TEXT,
            work_order_number TEXT,
            description TEXT,
            work_order_type TEXT,
            status TEXT,
            assigned_to TEXT,
            operation_description TEXT,
            operation_result TEXT,
            operation_comments TEXT,
            changed_by TEXT,
            status_date DATE,
            activity_number TEXT
        )''')

        # Map new Excel data to database columns
        new_df_mapped = pd.DataFrame({
            'site': new_df['Site'],
            'process': new_df['Process'],
            'factory': new_df['Factory'],
            'owning_department': new_df['Ownng Department'],
            'location': new_df['Location'],
            'equipment_id': new_df['Equipment ID'],
            'equipment': new_df['Equipment'],
            'work_order_number': new_df['WO No.'],
            'description': new_df['WO Description'],
            'work_order_type': new_df['WO Type'],
            'status': new_df['WO Status'],
            'assigned_to': new_df['Labor Name'],
            'operation_description': new_df['OP Description'],
            'operation_result': new_df['OP Result'],
            'operation_comments': new_df['OP Comments'],
            'changed_by': new_df['Changed by Name'],
            'status_date': new_df['Status Date'],
            'activity_number': new_df['Activity No']
        })

        # Get initial count
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM WorkOrder")
        initial_count = cursor.fetchone()[0]

        # Append new data to the table
        new_df_mapped.to_sql('WorkOrder', conn, if_exists='append', index=False)

        # Remove duplicates keeping the latest entry only if ALL columns match
        conn.execute('''
            CREATE TABLE WorkOrder_temp AS
            SELECT * FROM WorkOrder
            WHERE id IN (
                SELECT MAX(id)
                FROM WorkOrder
                GROUP BY site, process, factory, owning_department, location,
                         equipment_id, equipment, work_order_number, description,
                         work_order_type, status, assigned_to, operation_description,
                         operation_result, operation_comments, changed_by,
                         status_date, activity_number
            )
        ''')

        conn.execute('DROP TABLE WorkOrder')
        conn.execute('ALTER TABLE WorkOrder_temp RENAME TO WorkOrder')

        # Get final count
        cursor.execute("SELECT COUNT(*) FROM WorkOrder")
        final_count = cursor.fetchone()[0]

        # Calculate statistics
        total_records = final_count
        new_records = final_count - initial_count
        updated_records = len(new_df_mapped) - new_records

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': f'Upload complete: {new_records} new records added, {updated_records} records updated. Total records: {total_records}'
        })

    except Exception as e:
        logging.error(f"Error during file upload: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/check-workorders', methods=['GET'])
def check_workorders():
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Modified query to get crew from OrgChart table instead of employeelist
        cursor.execute("""
            SELECT 
                w.work_order_number, 
                w.assigned_to, 
                w.description,
                w.status, 
                w.status_date, 
                w.activity_number, 
                w.owning_department,
                w.stage,
                CASE
                    WHEN o.shift LIKE 'A-Crew%' THEN 'A'
                    WHEN o.shift LIKE 'B-Crew%' THEN 'B'
                    WHEN o.shift LIKE 'C-Crew%' THEN 'C'
                    WHEN o.shift LIKE 'D-Crew%' THEN 'D'
                    ELSE o.shift
                END as crew
            FROM WorkOrder w
            LEFT JOIN OrgChart o ON w.assigned_to = o.name
            ORDER BY crew, w.assigned_to, w.stage, w.status_date
        """)
        
        records = [dict(row) for row in cursor.fetchall()]
        
        # Log the number of records found for debugging
        print(f"Found {len(records)} work orders")
        
        conn.close()

        return jsonify({
            'status': 'success',
            'records': records
        })

    except Exception as e:
        print("Error in check_workorders:", str(e))
        return jsonify({
            'status': 'error',
            'message': str(e)
        })

@app.route('/analytics')
def analytics():
    return render_template('ganttchart.html')

@app.route('/authorization')
@login_required
def authorization():
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    cursor.execute('''CREATE TABLE IF NOT EXISTS Authorization (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        department TEXT NOT NULL,
        employee_login TEXT NOT NULL,
        access_level TEXT NOT NULL,
        UNIQUE(department, employee_login)
    )''')
    conn.commit()

    cursor.execute("SELECT * FROM Authorization")
    authorizations = [dict(row) for row in cursor.fetchall()]
    conn.close()

    return render_template('authorization.html', authorizations=authorizations)

@app.route('/api/authorizations', methods=['POST'])
def add_authorization():
    data = request.get_json()

    if not data or 'department' not in data or 'employee_login' not in data or 'access_level' not in data:
        return jsonify({'error': 'Missing required fields'}), 400

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        cursor.execute(
            "INSERT INTO Authorization (department, employee_login, access_level) VALUES (?, ?, ?)",
            (data['department'], data['employee_login'], data['access_level'])
        )
        conn.commit()

        # Return the new authorization data including the ID
        new_id = cursor.lastrowid
        return jsonify({
            'id': new_id,
            'department': data['department'],
            'employee_login': data['employee_login'],
            'access_level': data['access_level']
        }), 201
    except sqlite3.IntegrityError:
        return jsonify({'error': 'Authorization already exists'}), 409
    finally:
        conn.close()

@app.route('/api/authorizations/<int:auth_id>', methods=['DELETE'])
def delete_authorization(auth_id):
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    cursor.execute("DELETE FROM Authorization WHERE id = ?", (auth_id,))

    if cursor.rowcount == 0:
        conn.close()
        return jsonify({'error': 'Authorization not found'}), 404

    conn.commit()
    conn.close()

    return jsonify({'message': 'Authorization deleted successfully'})

@app.route('/calendar')
@login_required
def calendar():
    return render_template('calendar.html')

@app.route('/api/events', methods=['GET'])
def get_events():
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute("""
            SELECT
                work_order_number as id,
                description as title,
                status_date as start,
                DATETIME(status_date, '+1 hour') as end,  -- Set end time to 1 hour after start
                work_order_type as type,
                status,
                assigned_to
            FROM WorkOrder
            WHERE status_date IS NOT NULL
        """)
        events = [dict(row) for row in cursor.fetchall()]
        conn.close()

        return jsonify(events)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/events', methods=['POST'])
@login_required
def create_event():
    data = request.json
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        cursor.execute("""
            INSERT INTO Events (title, start_time, end_time, event_type)
            VALUES (?, ?, ?, ?)
        """, (data['title'], data['start'], data['end'], data['type']))
        conn.commit()
        event_id = cursor.lastrowid
        conn.close()
        return jsonify({'success': True, 'id': event_id})
    except Exception as e:
        conn.close()
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/events/<int:event_id>', methods=['DELETE', 'PUT'])
@login_required
def manage_event(event_id):
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        if request.method == 'DELETE':
            cursor.execute("DELETE FROM Events WHERE id = ?", (event_id,))
        else:  # PUT
            data = request.json
            cursor.execute("""
                UPDATE Events
                SET title = ?, start_time = ?, end_time = ?, event_type = ?
                WHERE id = ?
            """, (data['title'], data['start'], data['end'], data['type'], event_id))

        conn.commit()
        conn.close()
        return jsonify({'success': True})
    except Exception as e:
        conn.close()
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/download-flex-chart', methods=['GET'])
def download_flex_chart():
    try:
        # Define the path to the template file
        template_path = os.path.join('data', 'Facility Flex Chart template.xlsx')

        # Read the file into memory to avoid modifying the original
        with open(template_path, 'rb') as f:
            file_data = io.BytesIO(f.read())

        # Return it as a downloadable file
        return send_file(
            file_data,
            as_attachment=True,
            download_name='Facility Flex Chart LOCAL.xlsx',
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        print(f"Error during download: {e}")

@app.route('/api/org-chart', methods=['GET'])
def get_org_chart():
    conn = get_db_connection()
    try:
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM OrgChart')
        rows = cursor.fetchall()
        return jsonify([dict(row) for row in rows])
    finally:
        conn.close()

@app.route('/api/org-chart', methods=['POST'])
def update_org_chart():
    try:
        data = request.json
        conn = get_db_connection()
        cursor = conn.cursor()

        # Clear existing data
        cursor.execute('DELETE FROM OrgChart')

        # Try to insert with all columns
        for node in data:
            cursor.execute('''
                INSERT INTO OrgChart (id, parentId, name, title, position, department, shift, phone, image)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                node['id'],
                node['parentId'],
                node['name'],
                node.get('title', ''),
                node.get('position', ''),
                node.get('department', ''),  # Make sure department is included
                node.get('shift', ''),
                node.get('phone', ''),
                node.get('image', '')
            ))

        conn.commit()
        return jsonify({'success': True})
    except Exception as e:
        print(f"Error updating org chart: {e}")  # Add logging
        return jsonify({'success': False, 'error': str(e)}), 500
    finally:
        conn.close()

# Initialize the database table
def init_org_chart_table():
    conn = get_db_connection()
    try:
        cursor = conn.cursor()

        # Check if the table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='OrgChart'")
        table_exists = cursor.fetchone() is not None

        if table_exists:
            # Check if we need to drop the email column
            cursor.execute("PRAGMA table_info(OrgChart)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]

            if 'email' in column_names:
                print("Dropping email column from OrgChart table...")
                try:
                    # Create a new table without the email column
                    cursor.execute('''
                        CREATE TABLE OrgChart_new (
                            id TEXT PRIMARY KEY,
                            parentId TEXT,
                            name TEXT NOT NULL,
                            title TEXT,
                            position TEXT,
                            department TEXT,
                            shift TEXT,
                            phone TEXT,
                            image TEXT
                        )
                    ''')

                    # Copy data from the old table to the new one
                    cursor.execute('''
                        INSERT INTO OrgChart_new (id, parentId, name, title, position, department, shift, phone, image)
                        SELECT id, parentId, name, title, position, department, shift, phone, image FROM OrgChart
                    ''')

                    # Drop the old table
                    cursor.execute('DROP TABLE OrgChart')

                    # Rename the new table to the original name
                    cursor.execute('ALTER TABLE OrgChart_new RENAME TO OrgChart')

                    conn.commit()
                    print("Email column dropped successfully")
                except sqlite3.OperationalError as e:
                    print(f"Error dropping email column: {e}")
                    conn.rollback()

            # Check if the title column exists
            if 'title' not in column_names:
                print("Adding missing columns to OrgChart table...")
                try:
                    cursor.execute("ALTER TABLE OrgChart ADD COLUMN title TEXT")
                    cursor.execute("ALTER TABLE OrgChart ADD COLUMN shift TEXT")
                    conn.commit()
                    print("Columns added successfully")
                except sqlite3.OperationalError as e:
                    print(f"Error adding columns: {e}")

        # Create the table if it doesn't exist
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS OrgChart (
                id TEXT PRIMARY KEY,
                parentId TEXT,
                name TEXT NOT NULL,
                title TEXT,
                position TEXT,
                department TEXT,
                shift TEXT,
                phone TEXT,
                image TEXT
            )
        ''')

        # Check if table is empty and insert root node if it is
        cursor.execute('SELECT COUNT(*) FROM OrgChart')
        if cursor.fetchone()[0] == 0:
            cursor.execute('''
                INSERT INTO OrgChart (id, parentId, name, title, position, department, shift, phone, image)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                "1",
                "",
                "Ultium Cells Facility",
                "",
                "",
                "",
                "",
                "",
                "static/images/Ultium-New-Logo-icon.png"
            ))

        conn.commit()
    finally:
        conn.close()

# Call this during application startup
init_org_chart_table()

@app.route('/organization-chart')
@login_required
def organization_chart():
    return render_template('organization_chart.html')

@app.route('/test-sidebar')
def test_sidebar():
    return render_template('dashboard.html')

@app.route('/api/departments', methods=['GET'])
def get_departments():
    try:
        print("Fetching departments...")  # Debug log
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Get unique departments
        cursor.execute("""
            SELECT DISTINCT owning_department 
            FROM WorkOrder 
            WHERE owning_department IS NOT NULL AND owning_department != ''
            ORDER BY owning_department
        """)
        departments = [row[0] for row in cursor.fetchall()]
        
        conn.close()
        
        print(f"Found {len(departments)} departments")  # Debug log
        return jsonify(departments)
    except Exception as e:
        print(f"Error in get_departments: {str(e)}")  # Debug log
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/filtered-workorders', methods=['GET'])
def filtered_workorders():
    try:
        # Get filter parameters
        department = request.args.get('department')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        # Validate parameters
        if not department:
            return jsonify({
                'status': 'error',
                'message': 'Department parameter is required'
            }), 400
            
        # Connect to database
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Build query with parameters
        query = """
            SELECT work_order_number, assigned_to, description,
                   status, status_date, activity_number, owning_department, stage
            FROM WorkOrder
            WHERE owning_department = ?
        """
        params = [department]
        
        # Add date filters if provided
        if start_date:
            query += " AND status_date >= ?"
            params.append(start_date)
        
        if end_date:
            query += " AND status_date <= ?"
            params.append(end_date + " 23:59:59")  # Include the entire end day
            
        query += " ORDER BY assigned_to, stage, status_date"
        
        # Execute query
        cursor.execute(query, params)
        records = [dict(row) for row in cursor.fetchall()]
        
        # Log the number of records for debugging
        print(f"Filtered query returned {len(records)} records for department: {department}")
        
        conn.close()

        return jsonify({
            'status': 'success',
            'records': records
        })
    except Exception as e:
        print(f"Error in filtered_workorders: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/user-access')
@login_required
def get_user_access():
    # Get the current user's access level directly from the database
    # instead of relying on the session data which might be stale
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Get the current username from the session
        username = session.get('username', '')
        if not username:
            return jsonify({'access_level': '', 'department': ''})
            
        # Query the Authorization table for the latest access level
        cursor.execute("SELECT department, access_level FROM Authorization WHERE employee_login = ?", (username,))
        user_auth = cursor.fetchone()
        
        if user_auth:
            # Update the session with the latest data
            session['access_level'] = user_auth[1]  # access_level
            session['department'] = user_auth[0]    # department
            
            return jsonify({
                'access_level': user_auth[1],
                'department': user_auth[0]
            })
        else:
            return jsonify({'access_level': '', 'department': ''})
    except Exception as e:
        print(f"Error fetching user access level: {e}")
        return jsonify({'access_level': '', 'department': '', 'error': str(e)})
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    app.run(host="127.0.0.1", port=3001, debug=True)
