document.addEventListener('DOMContentLoaded', function() {
    const dp = new DayPilot.Calendar("dp", {
        viewType: "Week",
        headerDateFormat: "MMMM yyyy",
        businessBeginsHour: 0,
        businessEndsHour: 24,
        timeRangeSelectedHandling: "Enabled",
        onTimeRangeSelected: function(args) {
            // Handle time range selection
            const modal = new DayPilot.Modal({
                onClosed: function(args) {
                    dp.clearSelection();
                    if (args.result) {
                        dp.events.add(args.result);
                    }
                }
            });
        },
        onEventClick: function(args) {
            // Handle event click
            const modal = new DayPilot.Modal({
                onClosed: function(args) {
                    if (args.result) {
                        dp.events.update(args.result);
                    }
                }
            });
        },
        onBeforeEventRender: function(args) {
            // Customize event appearance based on status
            const status = args.data.status?.toLowerCase() || 'pending';
            args.data.backColor = getStatusColor(status);
            args.data.barHidden = true;
            args.data.html = `
                <div>
                    <div style="font-weight: bold;">${args.data.text}</div>
                    <div style="font-size: 0.8em;">WO: ${args.data.id}</div>
                    <div style="font-size: 0.8em;">Assigned: ${args.data.assigned_to || 'Unassigned'}</div>
                </div>
            `;
        }
    });

    // Load events from the WorkOrder table
    fetch('/api/events')
        .then(response => response.json())
        .then(data => {
            const events = data.map(wo => ({
                id: wo.id,
                text: wo.title,
                start: new DayPilot.Date(wo.start),
                end: new DayPilot.Date(wo.end),
                status: wo.status,
                assigned_to: wo.assigned_to,
                type: wo.type
            }));
            dp.events.list = events;
            dp.update();
        })
        .catch(error => console.error('Error loading work orders:', error));

    dp.init();

    function getStatusColor(status) {
        const colors = {
            'completed': '#4CAF50',
            'inprogress': '#2196F3',
            'pending': '#FFC107'
        };
        return colors[status] || colors.pending;
    }
});

